'use client';

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Play, RotateCcw } from 'lucide-react';
import { PNRRecord, BatchProcessingState } from '@/types';

interface PNRProcessorProps {
  pnrs: string[];
  onProcessingComplete: (results: PNRRecord[]) => void;
}

export function PNRProcessor({ pnrs, onProcessingComplete }: PNRProcessorProps) {
  const [state, setState] = useState<BatchProcessingState>({
    pnrs: pnrs.map(pnr => ({ pnr, status: 'pending', progress: 0 })),
    isProcessing: false,
    completedCount: 0,
    totalCount: pnrs.length
  });

  const processPNR = useCallback(async (pnr: string): Promise<void> => {
    try {
      // Update status to processing
      setState(prev => ({
        ...prev,
        pnrs: prev.pnrs.map(p =>
          p.pnr === pnr ? { ...p, status: 'processing', progress: 10 } : p
        )
      }));

      // Simulate progress updates
      for (let progress = 20; progress <= 90; progress += 20) {
        await new Promise(resolve => setTimeout(resolve, 500));
        setState(prev => ({
          ...prev,
          pnrs: prev.pnrs.map(p =>
            p.pnr === pnr ? { ...p, progress } : p
          )
        }));
      }

      // Call the API
      const response = await fetch('/api/process-pnr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pnr }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      // Update with completed status
      setState(prev => ({
        ...prev,
        pnrs: prev.pnrs.map(p =>
          p.pnr === pnr
            ? { ...p, status: 'completed', progress: 100, result }
            : p
        ),
        completedCount: prev.completedCount + 1
      }));

    } catch (error) {
      // Update with error status
      setState(prev => ({
        ...prev,
        pnrs: prev.pnrs.map(p =>
          p.pnr === pnr
            ? {
                ...p,
                status: 'error',
                progress: 0,
                error: error instanceof Error ? error.message : 'Unknown error'
              }
            : p
        ),
        completedCount: prev.completedCount + 1
      }));
    }
  }, []);

  const startProcessing = useCallback(async () => {
    setState(prev => ({ ...prev, isProcessing: true, completedCount: 0 }));

    // Process PNRs sequentially to avoid overwhelming the APIs
    for (const pnrRecord of state.pnrs) {
      if (pnrRecord.status === 'pending') {
        await processPNR(pnrRecord.pnr);
      }
    }

    // Get the final state after all processing is complete
    setState(prev => {
      const finalState = { ...prev, isProcessing: false };

      // Notify parent component with the completed results
      // Use setTimeout to ensure state update is complete
      setTimeout(() => {
        onProcessingComplete(finalState.pnrs);
      }, 0);

      return finalState;
    });
  }, [state.pnrs, processPNR, onProcessingComplete]);

  const resetProcessing = useCallback(() => {
    setState({
      pnrs: pnrs.map(pnr => ({ pnr, status: 'pending', progress: 0 })),
      isProcessing: false,
      completedCount: 0,
      totalCount: pnrs.length
    });
  }, [pnrs]);

  const getStatusBadgeVariant = (status: PNRRecord['status']) => {
    switch (status) {
      case 'pending': return 'secondary';
      case 'processing': return 'default';
      case 'completed': return 'default';
      case 'error': return 'destructive';
      default: return 'secondary';
    }
  };



  const overallProgress = state.totalCount > 0
    ? (state.completedCount / state.totalCount) * 100
    : 0;

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Process PNR Records</span>
          <div className="flex gap-2">
            <Button
              onClick={startProcessing}
              disabled={state.isProcessing}
              size="sm"
            >
              <Play className="h-4 w-4 mr-2" />
              {state.isProcessing ? 'Processing...' : 'Start Processing'}
            </Button>
            <Button
              onClick={resetProcessing}
              disabled={state.isProcessing}
              variant="outline"
              size="sm"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          Process {state.totalCount} PNR records to extract insurance data
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Overall Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{state.completedCount} / {state.totalCount}</span>
          </div>
          <Progress value={overallProgress} className="w-full" />
        </div>

        {/* Individual PNR Status */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {state.pnrs.map((pnrRecord) => (
            <div
              key={pnrRecord.pnr}
              className="flex items-center justify-between p-3 border rounded-lg"
            >
              <div className="flex items-center gap-3">
                <span className="font-mono font-medium">{pnrRecord.pnr}</span>
                <Badge variant={getStatusBadgeVariant(pnrRecord.status)}>
                  {pnrRecord.status}
                </Badge>
              </div>

              <div className="flex items-center gap-3">
                {pnrRecord.status === 'processing' && (
                  <div className="w-24">
                    <Progress value={pnrRecord.progress} className="h-2" />
                  </div>
                )}

                {pnrRecord.status === 'completed' && pnrRecord.result && (
                  <span className="text-sm text-green-600">
                    {pnrRecord.result.summary.missingConfirmation} missing
                  </span>
                )}

                {pnrRecord.status === 'error' && (
                  <span className="text-sm text-red-600 max-w-xs truncate">
                    {pnrRecord.error}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        {state.completedCount > 0 && (
          <Alert>
            <AlertDescription>
              Completed: {state.pnrs.filter(p => p.status === 'completed').length},
              Errors: {state.pnrs.filter(p => p.status === 'error').length},
              Remaining: {state.pnrs.filter(p => p.status === 'pending').length}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
