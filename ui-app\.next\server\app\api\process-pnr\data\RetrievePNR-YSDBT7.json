{"seriesNum": "299", "PNR": "YSDBT7", "bookAgent": "WEB_MOBILE", "resCurrency": "QAR", "PNRPin": "82559607", "bookDate": "2025-05-05T12:34:39", "modifyDate": "2025-05-10T08:32:27", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "3808s57ceeg3u6nef863a6k44685ad39805dfd5e65ea", "securityGUID": "3808s57ceeg3u6nef863a6k44685ad39805dfd5e65ea", "lastLoadGUID": "34C4E2F91BCF03E5E0631E206F0AAB9C", "isAsyncPNR": false, "MasterPNR": "", "segments": [{"segKey": "16866765:16866765:5/10/2025 10:00:00 AM", "LFID": 16866765, "depDate": "2025-05-10T00:00:00", "flightGroupId": "16866765", "org": "DOH", "dest": "HAS", "depTime": "2025-05-10T10:00:00", "depTimeGMT": "2025-05-10T07:00:00", "arrTime": "2025-05-10T15:15:00", "operCarrier": "FZ", "operFlightNum": "002/821", "mrktCarrier": "FZ ", "mrktFlightNum": "002/821", "persons": [{"recNum": 2, "status": 5}], "legDetails": [{"PFID": 180998, "depDate": "2025-05-10T10:00:00", "legKey": "16866765:180998:5/10/2025 10:00:00 AM", "customerKey": "A110612CDF35628BB585795D9A6E3383E2B1CC619D0FC213F4FC26D325FC1AC4"}, {"PFID": 186398, "depDate": "2025-05-10T13:55:00", "legKey": "16866765:186398:5/10/2025 1:55:00 PM", "customerKey": "DD1719DAF77833B75708E61D12FCC6F435C72334711B2515446DDE26F8682BA9"}], "active": true, "changeType": "AC"}, {"segKey": "16866770:16866770:5/8/2025 10:00:00 AM", "LFID": 16866770, "depDate": "2025-05-08T00:00:00", "flightGroupId": "16866770", "org": "DOH", "dest": "HAS", "depTime": "2025-05-08T10:00:00", "depTimeGMT": "2025-05-08T07:00:00", "arrTime": "2025-05-08T15:15:00", "operCarrier": "FZ", "operFlightNum": "002/821", "mrktCarrier": "FZ ", "mrktFlightNum": "002/821", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 180998, "depDate": "2025-05-08T10:00:00", "legKey": "16866770:180998:5/8/2025 10:00:00 AM", "customerKey": "04E0CCB508C725A47F78CDB146D23C72BEEC8038F74B4B5E85A096742E2E274B"}, {"PFID": 186398, "depDate": "2025-05-08T13:55:00", "legKey": "16866770:186398:5/8/2025 1:55:00 PM", "customerKey": "4F0BDE69EE949A97AE2B794EEF366531254F0EC8DD4B60639F6CA0413D8FB3C0"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 267951383, "fName": "MUHANNAD", "lName": "ALRWAILY", "title": "MR", "PTCID": 1, "gender": "M", "DOB": "1991-06-10T00:00:00", "nationality": "682", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB_MOBILE", "cancelAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "B", "insuPurchasedate": "5/5/2025 12:34:13 PM", "provider": "AIG", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuConfNum": "987202245", "insuTransID": "3c23bc7c-179b-4793-bf67-36d7a21ab705", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6818af320007780000001f84#1#1#MOBILE#VAYANT#CREATE", "fareTypeID": 12, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-05T12:34:39"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB_MOBILE", "statusReasonID": 0, "markFareClass": "B", "status": 5, "fareClass": "B", "operFareClass": "B", "FBC": "BOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "FFSNR-XEHFL-INS/9b01415a-5f81-41fb-bb43-8b444095e36f", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681b4db900077700000052db#267951383#1#MOBILE#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 12, "bookDate": "2025-05-07T12:11:40"}]}], "payments": [{"paymentID": *********, "paxID": 267951383, "method": "ETAP", "status": "1", "paidDate": "2025-05-10T08:32:27", "cardNum": "1305", "IATANum": "96007837", "paidCurr": "AED", "paidAmt": 425.25, "baseCurr": "QAR", "baseAmt": 437.18, "userID": "iuliia.moroz", "channelID": 19, "paymentComment": "MasterCard", "correlationId": "b6755a91544e47ydy4uafds46bzat1435b489d6646e2", "reference": "175624", "POSAirport": "DXB", "workStationID": "WSFZ6941", "tranId": "1", "deviceTerminalID": 45205015, "RRNNumber": "513008010267", "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": "http://eze.cc/RZPPOS/t/a/kp7d3JGY/", "autoRefundStatus": null, "merchantID": "200000006184", "exchangeRate": "1.02804507", "resExternalPaymentID": 1}, {"paymentID": 207960955, "paxID": 267951526, "method": "IPAY", "status": "2", "paidDate": "2025-05-05T12:34:46", "cardNum": "************1305", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1105.19, "baseCurr": "QAR", "baseAmt": 1105.19, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "reference": "22849800", "externalReference": "22849800", "tranId": "21221684", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21221684}, {"paymentID": *********, "paxID": 267951539, "method": "IPAY", "status": "1", "paidDate": "2025-05-05T12:35:26", "cardNum": "************0462", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1105.19, "baseCurr": "QAR", "baseAmt": 1105.19, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "153525", "reference": "22849820", "externalReference": "22849820", "tranId": "21221684", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21221684}, {"paymentID": *********, "paxID": 268194816, "method": "IPAY", "status": "1", "paidDate": "2025-05-07T12:12:17", "cardNum": "************0462", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 819.25, "baseCurr": "QAR", "baseAmt": 819.25, "userID": "WEB_MOBILE", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "151216", "reference": "22891466", "externalReference": "22891466", "tranId": "21266382", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21266382}], "OAFlights": null, "physicalFlights": [{"key": "16866770:180998:2025-05-08T10:00:00 AM", "LFID": 16866770, "PFID": 180998, "org": "DOH", "dest": "DXB", "depDate": "2025-05-08T10:00:00", "depTime": "2025-05-08T10:00:00", "arrTime": "2025-05-08T12:15:00", "carrier": "FZ", "flightNum": "002", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "002", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "5/2/2025 6:35:17 AM"}, {"key": "16866770:186398:2025-05-08T01:55:00 PM", "LFID": 16866770, "PFID": 186398, "org": "DXB", "dest": "HAS", "depDate": "2025-05-08T13:55:00", "depTime": "2025-05-08T13:55:00", "arrTime": "2025-05-08T15:15:00", "carrier": "FZ", "flightNum": "821", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "821", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "HAS", "operatingCarrier": "FZ", "flightDuration": 8400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON>l", "isActive": false, "changeType": "AC", "flightChangeTime": "5/2/2025 6:35:17 AM"}, {"key": "16866765:180998:2025-05-10T10:00:00 AM", "LFID": 16866765, "PFID": 180998, "org": "DOH", "dest": "DXB", "depDate": "2025-05-10T10:00:00", "depTime": "2025-05-10T10:00:00", "arrTime": "2025-05-10T12:15:00", "carrier": "FZ", "flightNum": "002", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "002", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "AC", "flightChangeTime": "4/29/2025 11:16:14 AM"}, {"key": "16866765:186398:2025-05-10T01:55:00 PM", "LFID": 16866765, "PFID": 186398, "org": "DXB", "dest": "HAS", "depDate": "2025-05-10T13:55:00", "depTime": "2025-05-10T13:55:00", "arrTime": "2025-05-10T15:15:00", "carrier": "FZ", "flightNum": "821", "depTerminal": "Terminal 2", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "821", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "HAS", "operatingCarrier": "FZ", "flightDuration": 8400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "<PERSON>l", "isActive": false, "changeType": "AC", "flightChangeTime": "4/29/2025 11:16:14 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-05T12:34:39", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T12:34:39"}, {"chargeID": **********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332802523, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1335944067, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332802520, "paymentMap": [{"key": "1335944067:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1335944071, "codeType": "TAX", "taxID": 11747, "taxCode": "IO", "taxChargeID": **********, "amt": -90, "curr": "QAR", "originalAmt": -90, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "International Airport Building Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332802526, "paymentMap": [{"key": "1335944071:*********", "paymentID": *********, "amt": -90, "approveCode": 0}]}, {"chargeID": 1335944058, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332802521, "paymentMap": [{"key": "1335944058:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1335944065, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332802524, "paymentMap": [{"key": "1335944065:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1335944063, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -40, "curr": "QAR", "originalAmt": -40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332802527, "paymentMap": [{"key": "1335944063:*********", "paymentID": *********, "amt": -40, "approveCode": 0}]}, {"chargeID": 1335944070, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1332802522, "paymentMap": [{"key": "1335944070:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1332802521, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332802521:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1332802523, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332802523:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1332802527, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332802527:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1332802524, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332802524:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1332802520, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332802520:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1332802525, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "Security Charges", "comment": "Security Charges", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332802525:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1332802522, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332802522:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1332802526, "codeType": "TAX", "taxID": 11747, "taxCode": "IO", "taxChargeID": **********, "amt": 90, "curr": "QAR", "originalAmt": 90, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "International Airport Building Charge", "comment": "International Airport Building Charge", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332802526:*********", "paymentID": *********, "amt": 90, "approveCode": 0}]}, {"chargeID": 1335944062, "codeType": "AIR", "amt": -700, "curr": "QAR", "originalAmt": -700, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-07T12:11:41", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1335944062:*********", "paymentID": *********, "amt": -700, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 700, "curr": "QAR", "originalAmt": 700, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-05T12:34:39", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 700, "approveCode": 0}]}, {"chargeID": 1332805024, "codeType": "PMNT", "amt": 32.19, "curr": "QAR", "originalAmt": 32.19, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-05T12:35:33", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1332805024:*********", "paymentID": *********, "amt": 32.19, "approveCode": 0}]}, {"chargeID": 1335944072, "codeType": "PNLT", "amt": 740, "curr": "QAR", "originalAmt": 740, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-07T12:11:41", "desc": "Penalty AddedDueToModify FZ  002 DOH  - DXB  08-May-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944072:*********", "paymentID": *********, "amt": 740, "approveCode": 0}]}, {"chargeID": 1332802528, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1332802552, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186398"}, {"chargeID": 1332802553, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-05T12:34:39", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "180998"}]}, {"recNum": 2, "charges": [{"chargeID": 1335944119, "codeType": "INSU", "amt": 35.39, "curr": "QAR", "originalAmt": 35.39, "originalCurr": "QAR", "status": 1, "exchRate": 1, "billDate": "2025-05-07T12:11:41", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944119:*********", "paymentID": *********, "amt": 35.39, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-07T12:11:41"}, {"chargeID": 1339730472, "codeType": "TAX", "taxID": 13659, "taxCode": "K9", "taxChargeID": 1339730471, "amt": 20.05, "curr": "QAR", "originalAmt": 20.05, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T08:31:34", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "chargeSequence": "1412801300263", "paymentRefStatus": 1, "paymentMap": [{"key": "1339730472:*********", "paymentID": *********, "amt": 20.05, "approveCode": 0, "saleCurrency": "AED", "saleAmount": 19.5}], "PFID": "186398", "saleCurrency": "AED", "saleAmount": 19.5, "POSAirport": "DXB", "workStationID": "WSFZ6941", "parameter1Name": "BATCH_ID", "parameter1Value": "e5dee620ar2be4b0281a424a99e5u539y264dfwd4cdb", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-10T13:55:00\",\"fltNum\":\"821\",\"depDate\":\"2025-05-10T00:00:00\",\"board\":\"DXB\",\"off\":\"HAS\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1142908421_DXBHASHAS"}, {"chargeID": 1339730473, "codeType": "TAX", "taxID": 13659, "taxCode": "K9", "taxChargeID": 1339730471, "amt": 0.77, "curr": "QAR", "originalAmt": 0.77, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T08:31:34", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "chargeSequence": "1412801300263", "paymentRefStatus": 1, "paymentMap": [{"key": "1339730473:*********", "paymentID": *********, "amt": 0.77, "approveCode": 0, "saleCurrency": "AED", "saleAmount": 0.75}], "PFID": "186398", "saleCurrency": "AED", "saleAmount": 0.75, "POSAirport": "DXB", "workStationID": "WSFZ6941", "parameter1Name": "BATCH_ID", "parameter1Value": "e5dee620ar2be4b0281a424a99e5u539y264dfwd4cdb", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-10T13:55:00\",\"fltNum\":\"821\",\"depDate\":\"2025-05-10T00:00:00\",\"board\":\"DXB\",\"off\":\"HAS\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1142908421_DXBHASHAS"}, {"chargeID": 1335944047, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1335944044, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944047:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1335944093, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1335944044, "amt": 40, "curr": "QAR", "originalAmt": 40, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944093:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1335944091, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1335944044, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Security Charges", "comment": "Security Charges", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944091:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1335944089, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1335944044, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944089:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1335944090, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1335944044, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944090:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1335944048, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1335944044, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944048:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1335944046, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1335944044, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944046:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1335944092, "codeType": "TAX", "taxID": 11747, "taxCode": "IO", "taxChargeID": 1335944044, "amt": 90, "curr": "QAR", "originalAmt": 90, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "International Airport Building Charge", "comment": "International Airport Building Charge", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944092:*********", "paymentID": *********, "amt": 50, "approveCode": 0}, {"key": "1335944092:*********", "paymentID": *********, "amt": 40, "approveCode": 0}]}, {"chargeID": 1335944045, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1335944044, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944045:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1335944044, "codeType": "AIR", "amt": 710, "curr": "QAR", "originalAmt": 710, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-07T12:11:41", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335944044:*********", "paymentID": *********, "amt": 710, "approveCode": 0}]}, {"chargeID": 1335947504, "codeType": "PMNT", "amt": 23.86, "curr": "QAR", "originalAmt": 23.86, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-07T12:12:22", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1335947504:*********", "paymentID": *********, "amt": 23.86, "approveCode": 0}]}, {"chargeID": 1339730474, "codeType": "GHA", "taxChargeID": 1339730471, "amt": 15.42, "curr": "QAR", "originalAmt": 15.42, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T08:31:34", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "chargeSequence": "1412801300263", "paymentRefStatus": 1, "paymentMap": [{"key": "1339730474:*********", "paymentID": *********, "amt": 15.42, "approveCode": 0, "saleCurrency": "AED", "saleAmount": 15}], "PFID": "186398", "saleCurrency": "AED", "saleAmount": 15, "POSAirport": "DXB", "workStationID": "WSFZ6941", "parameter1Name": "BATCH_ID", "parameter1Value": "e5dee620ar2be4b0281a424a99e5u539y264dfwd4cdb", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-10T13:55:00\",\"fltNum\":\"821\",\"depDate\":\"2025-05-10T00:00:00\",\"board\":\"DXB\",\"off\":\"HAS\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1142908421_DXBHASHAS"}, {"chargeID": 1335944094, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1335944044, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1335944101, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "180998"}, {"chargeID": 1335944100, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 1, "exchRate": 0, "billDate": "2025-05-07T12:11:41", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "186398"}, {"chargeID": 1339730471, "codeType": "LNGS", "amt": 400.94, "curr": "QAR", "originalAmt": 400.94, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T08:31:34", "comment": "FLXID:FLX_0000_DEF_ID:", "reasonID": 12, "channelID": 19, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "chargeSequence": "1412801300263", "paymentRefStatus": 1, "paymentMap": [{"key": "1339730471:*********", "paymentID": *********, "amt": 400.94, "approveCode": 0, "saleCurrency": "AED", "saleAmount": 390}], "PFID": "186398", "saleCurrency": "AED", "saleAmount": 390, "POSAirport": "DXB", "workStationID": "WSFZ6941", "parameter1Name": "BATCH_ID", "parameter1Value": "e5dee620ar2be4b0281a424a99e5u539y264dfwd4cdb", "parameter2Name": "ROUTE", "parameter2Value": "[{\"carrier\":\"FZ\",\"std\":\"2025-05-10T13:55:00\",\"fltNum\":\"821\",\"depDate\":\"2025-05-10T00:00:00\",\"board\":\"DXB\",\"off\":\"HAS\"}]", "parameter3Name": "RECEIPT_GROUP", "parameter3Value": "1142908421_DXBHASHAS"}]}], "parentPNRs": [], "childPNRs": []}