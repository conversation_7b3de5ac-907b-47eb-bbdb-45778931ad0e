'use client';

import React, { useState, useCallback } from 'react';
import <PERSON> from 'papaparse';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Upload, FileText, X, ChevronDown, ChevronUp } from 'lucide-react';

interface CSVUploadProps {
  onPNRsExtracted: (pnrs: string[]) => void;
}

interface CSVData {
  [key: string]: string;
}

export function CSVUpload({ onPNRsExtracted }: CSVUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<CSVData[]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [selectedColumn, setSelectedColumn] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [pnrs, setPnrs] = useState<string[]>([]);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files?.[0];
    if (!uploadedFile) return;

    if (!uploadedFile.name.toLowerCase().endsWith('.csv')) {
      setError('Please upload a CSV file');
      return;
    }

    setFile(uploadedFile);
    setError('');

    // First, read the file as text to handle simple PNR lists
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      if (!text) {
        setError('Failed to read file content');
        return;
      }

      // Split by lines and clean up
      const lines = text.split(/\r?\n/).map(line => line.trim()).filter(line => line.length > 0);

      // Check if this looks like a simple PNR list (no commas, just one value per line)
      const hasCommas = text.includes(',');

      // Check if the data lines (excluding potential header) look like PNRs
      const dataLines = lines.length > 1 ? lines.slice(1) : lines; // Skip first line as potential header
      const looksLikePNRList = !hasCommas && dataLines.every(line => {
        const trimmed = line.trim();
        return trimmed.length >= 3 &&
               trimmed.length <= 10 &&
               /^[A-Z0-9]+$/i.test(trimmed);
      });

      if (looksLikePNRList) {
        // Handle simple PNR list format
        // Skip the first line if it looks like a header (contains non-PNR characters or is too long)
        const firstLine = lines[0]?.trim();
        const firstLineIsHeader = firstLine && (
          firstLine.length > 10 ||
          !/^[A-Z0-9]+$/i.test(firstLine) ||
          firstLine.toLowerCase().includes('pnr') ||
          firstLine.toLowerCase().includes('confirmation') ||
          firstLine.toLowerCase().includes('booking') ||
          firstLine.toLowerCase().includes('code')
        );

        const pnrLines = firstLineIsHeader ? lines.slice(1) : lines;
        const extractedPnrs = pnrLines
          .map(line => line.trim().toUpperCase())
          .filter(pnr => pnr && pnr.length >= 3 && pnr.length <= 10)
          .filter((pnr, index, array) => array.indexOf(pnr) === index); // Remove duplicates

        if (extractedPnrs.length === 0) {
          setError('No valid PNR numbers found in the file');
          return;
        }

        setPnrs(extractedPnrs);
        onPNRsExtracted(extractedPnrs);
        setError('');
        setIsCollapsed(true); // Auto-collapse after successful extraction

        // Set dummy data for display purposes
        setCsvData([]);
        setHeaders([]);
        setSelectedColumn('');
      } else {
        // Handle structured CSV format with Papa Parse
        Papa.parse(uploadedFile, {
          header: true,
          skipEmptyLines: true,
          delimiter: hasCommas ? ',' : '\t', // Try tab if no commas
          complete: (results) => {
            if (results.errors.length > 0) {
              // Filter out delimiter detection warnings
              const realErrors = results.errors.filter(error =>
                !error.message.includes('Unable to auto-detect delimiting character')
              );

              if (realErrors.length > 0) {
                setError(`CSV parsing error: ${realErrors[0].message}`);
                return;
              }
            }

            const data = results.data as CSVData[];
            setCsvData(data);

            if (data.length > 0) {
              const csvHeaders = Object.keys(data[0]);
              setHeaders(csvHeaders);

              // Auto-select PNR column if found
              const pnrColumn = csvHeaders.find(header =>
                header.toLowerCase().includes('pnr') ||
                header.toLowerCase().includes('confirmation') ||
                header.toLowerCase().includes('booking')
              );
              if (pnrColumn) {
                setSelectedColumn(pnrColumn);
              }
            }
          },
          error: (error) => {
            setError(`Failed to parse CSV: ${error.message}`);
          }
        });
      }
    };

    reader.onerror = () => {
      setError('Failed to read file');
    };

    reader.readAsText(uploadedFile);
  }, [onPNRsExtracted]);

  const extractPNRs = useCallback(() => {
    if (!selectedColumn || csvData.length === 0) {
      setError('Please select a column containing PNR numbers');
      return;
    }

    const extractedPnrs = csvData
      .map(row => row[selectedColumn]?.trim().toUpperCase())
      .filter(pnr => pnr && pnr.length >= 3 && pnr.length <= 10)
      .filter((pnr, index, array) => array.indexOf(pnr) === index); // Remove duplicates

    if (extractedPnrs.length === 0) {
      setError('No valid PNR numbers found in the selected column');
      return;
    }

    setPnrs(extractedPnrs);
    onPNRsExtracted(extractedPnrs);
    setError('');
    setIsCollapsed(true); // Auto-collapse after successful extraction
  }, [selectedColumn, csvData, onPNRsExtracted]);

  const clearFile = useCallback(() => {
    setFile(null);
    setCsvData([]);
    setHeaders([]);
    setSelectedColumn('');
    setPnrs([]);
    setError('');
    setIsCollapsed(false); // Expand when clearing
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload CSV File
            {pnrs.length > 0 && (
              <Badge variant="secondary">
                {pnrs.length} PNRs loaded
              </Badge>
            )}
          </div>
          {pnrs.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="flex items-center gap-1"
            >
              {isCollapsed ? (
                <>
                  <span className="text-sm">Expand</span>
                  <ChevronDown className="h-4 w-4" />
                </>
              ) : (
                <>
                  <span className="text-sm">Collapse</span>
                  <ChevronUp className="h-4 w-4" />
                </>
              )}
            </Button>
          )}
        </CardTitle>
        {!isCollapsed && (
          <CardDescription>
            Upload a CSV file containing PNR numbers. Supports both simple PNR lists (one per line) and structured CSV files with headers.
          </CardDescription>
        )}
      </CardHeader>

      {/* Collapsed summary */}
      {isCollapsed && pnrs.length > 0 && (
        <CardContent className="pt-0">
          <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">
                {file?.name || 'CSV File'}
              </span>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                {pnrs.length} PNRs ready
              </Badge>
            </div>
            <Button variant="ghost" size="sm" onClick={clearFile} className="text-green-700 hover:text-green-900">
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      )}

      {!isCollapsed && (
        <CardContent className="space-y-4">
        {!file ? (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                Choose a CSV file or drag and drop it here
              </p>
              <Input
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="max-w-xs mx-auto"
              />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span className="text-sm font-medium">{file.name}</span>
                <Badge variant="secondary">
                  {pnrs.length > 0 ? `${pnrs.length} PNRs` : `${csvData.length} rows`}
                </Badge>
              </div>
              <Button variant="ghost" size="sm" onClick={clearFile}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Show column selection only for structured CSV files */}
            {headers.length > 0 && pnrs.length === 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Select column containing PNR numbers:
                </label>
                <select
                  value={selectedColumn}
                  onChange={(e) => setSelectedColumn(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">-- Select Column --</option>
                  {headers.map((header) => (
                    <option key={header} value={header}>
                      {header}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {selectedColumn && pnrs.length === 0 && (
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Preview of {selectedColumn} column (first 5 rows):
                </p>
                <div className="bg-gray-50 p-3 rounded-lg">
                  {csvData.slice(0, 5).map((row, index) => (
                    <div key={index} className="text-sm">
                      {row[selectedColumn]}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Show extract button only for structured CSV files that haven't been processed */}
            {headers.length > 0 && pnrs.length === 0 && (
              <Button
                onClick={extractPNRs}
                disabled={!selectedColumn}
                className="w-full"
              >
                Extract PNR Numbers ({selectedColumn ? csvData.length : 0} rows)
              </Button>
            )}

            {/* Show success message for simple PNR list files */}
            {pnrs.length > 0 && headers.length === 0 && (
              <Alert>
                <AlertDescription>
                  ✅ Automatically detected and processed simple PNR list format.
                  Found {pnrs.length} unique PNR numbers.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {pnrs.length > 0 && (
          <Alert>
            <AlertDescription>
              Successfully extracted {pnrs.length} unique PNR numbers: {pnrs.slice(0, 5).join(', ')}
              {pnrs.length > 5 && ` and ${pnrs.length - 5} more...`}
            </AlertDescription>
          </Alert>
        )}
        </CardContent>
      )}
    </Card>
  );
}
