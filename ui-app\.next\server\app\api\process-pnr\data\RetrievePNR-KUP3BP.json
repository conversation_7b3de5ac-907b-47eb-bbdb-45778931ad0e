{"seriesNum": "299", "PNR": "KUP3BP", "bookAgent": "MOBILE_APP", "resCurrency": "EUR", "PNRPin": "82143911", "bookDate": "2025-04-20T04:12:53", "modifyDate": "2025-05-23T03:16:43", "resType": "MOBILE", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "b925fka63883pdq448e944i8v4t400pfm895049603f7", "securityGUID": "b925fka63883pdq448e944i8v4t400pfm895049603f7", "lastLoadGUID": "ed9ef9ba-b6cd-4bef-8481-53e6f5ad4f78", "isAsyncPNR": false, "MasterPNR": "KUP3BP", "segments": [{"segKey": "16087926:16087926:5/16/2025 3:25:00 PM", "LFID": 16087926, "depDate": "2025-05-16T00:00:00", "flightGroupId": "16087926", "org": "SOF", "dest": "DXB", "depTime": "2025-05-16T15:25:00", "depTimeGMT": "2025-05-16T12:25:00", "arrTime": "2025-05-16T21:25:00", "operCarrier": "FZ", "operFlightNum": "1758", "mrktCarrier": "FZ ", "mrktFlightNum": "1758", "persons": [{"recNum": 1, "status": 5}], "legDetails": [{"PFID": 181676, "depDate": "2025-05-16T15:25:00", "legKey": "16087926:181676:5/16/2025 3:25:00 PM", "customerKey": "C862FED8080BC84C79F0B8F0DA50741935E2DD7A12A4A3ECE35BD798F14D90B8"}], "active": true}, {"segKey": "16087936:16087936:5/30/2025 10:00:00 AM", "LFID": 16087936, "depDate": "2025-05-30T00:00:00", "flightGroupId": "16087936", "org": "DXB", "dest": "SOF", "depTime": "2025-05-30T10:00:00", "depTimeGMT": "2025-05-30T06:00:00", "arrTime": "2025-05-30T14:25:00", "operCarrier": "FZ", "operFlightNum": "1757", "mrktCarrier": "FZ ", "mrktFlightNum": "1757", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181686, "depDate": "2025-05-30T10:00:00", "legKey": "16087936:181686:5/30/2025 10:00:00 AM", "customerKey": "0E7635ED1513F8DBA4318F13CBA0CEECE399A9D66BD5006E1F77FE91C91D0681"}], "active": true}, {"segKey": "16087936:16087936:5/23/2025 10:00:00 AM", "LFID": 16087936, "depDate": "2025-05-23T00:00:00", "flightGroupId": "16087936", "org": "DXB", "dest": "SOF", "depTime": "2025-05-23T10:00:00", "depTimeGMT": "2025-05-23T06:00:00", "arrTime": "2025-05-23T14:25:00", "operCarrier": "FZ", "operFlightNum": "1757", "mrktCarrier": "FZ ", "mrktFlightNum": "1757", "persons": [{"recNum": 5, "status": 5}], "legDetails": [{"PFID": 181686, "depDate": "2025-05-23T10:00:00", "legKey": "16087936:181686:5/23/2025 10:00:00 AM", "customerKey": "85715C617D07D3D97D9EBD65FF753705F50D5DCC232E929C0EAA362BA4538E2A"}], "active": true}, {"segKey": "16087936:16087936:5/25/2025 10:00:00 AM", "LFID": 16087936, "depDate": "2025-05-25T00:00:00", "flightGroupId": "16087936", "org": "DXB", "dest": "SOF", "depTime": "2025-05-25T10:00:00", "depTimeGMT": "2025-05-25T06:00:00", "arrTime": "2025-05-25T14:25:00", "operCarrier": "FZ", "operFlightNum": "1757", "mrktCarrier": "FZ ", "mrktFlightNum": "1757", "persons": [{"recNum": 4, "status": 0}], "legDetails": [{"PFID": 181686, "depDate": "2025-05-25T10:00:00", "legKey": "16087936:181686:5/25/2025 10:00:00 AM", "customerKey": "11C1248534F9BBF6DA24E82236BE4A27F4ABAE70AE59BF30BE4FF2BF330E8C4F"}], "active": true}, {"segKey": "16087936:16087936:5/28/2025 10:00:00 AM", "LFID": 16087936, "depDate": "2025-05-28T00:00:00", "flightGroupId": "16087936", "org": "DXB", "dest": "SOF", "depTime": "2025-05-28T10:00:00", "depTimeGMT": "2025-05-28T06:00:00", "arrTime": "2025-05-28T14:25:00", "operCarrier": "FZ", "operFlightNum": "1757", "mrktCarrier": "FZ ", "mrktFlightNum": "1757", "persons": [{"recNum": 3, "status": 0}], "legDetails": [{"PFID": 181686, "depDate": "2025-05-28T10:00:00", "legKey": "16087936:181686:5/28/2025 10:00:00 AM", "customerKey": "97ADBC5AAF98697BF315DB052B49B3FCA98E51C8EC5F8E22780E31FA4058EC5B"}], "active": true}], "persons": [{"paxID": 266345362, "fName": "STILIANA", "lName": "DINCHEVA", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1973-02-03T00:00:00", "nationality": "100", "FFNum": "446372216", "FFTier": "BLUE", "TierID": "3", "recNum": [1, 2, 3, 4, 5]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "V", "status": 5, "fareClass": "V", "operFareClass": "V", "FBC": "VRL8BG5", "fareBrand": "Flex", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6824fe9e00077800000054a0#266345362#1#MOBILE#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-20T04:12:53"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRL8BG2", "fareBrand": "Flex", "cabin": "ECONOMY", "toRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "680473f1000778000000c16a#1#2#MOBILE#VAYANT#CREATE", "fareTypeID": 13, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-04-20T04:12:53"}]}, {"recNum": 3, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "B", "status": 0, "fareClass": "B", "operFareClass": "B", "FBC": "BRL8BG2", "fareBrand": "Flex", "cabin": "ECONOMY", "toRecNum": 4, "fromRecNum": 2, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "68199ef80007780000002ec4#266345362#2#MOBILE#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-06T05:33:15"}]}, {"recNum": 4, "recordDetails": [{"bookAgent": "MOBILE_APP", "cancelAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "L", "status": 0, "fareClass": "L", "operFareClass": "L", "FBC": "LRL8BG5", "fareBrand": "Flex", "cabin": "ECONOMY", "toRecNum": 5, "fromRecNum": 3, "fareCarrier": "FZ", "primaryPax": false, "pricingKey": "6824fe9e00077800000054a0#266345362#2#MOBILE#OneSearch#CHANGE", "fareTypeID": 13, "channelID": 12, "cancelReasonID": 0, "bookDate": "2025-05-14T20:36:45"}]}, {"recNum": 5, "recordDetails": [{"bookAgent": "MOBILE_APP", "statusReasonID": 0, "markFareClass": "L", "insuPurchasedate": "5/17/2025 5:05:58 PM", "provider": "<PERSON>", "status": 5, "fareClass": "L", "operFareClass": "L", "FBC": "LRL8BG5", "fareBrand": "Flex", "cabin": "ECONOMY", "discloseEmergencyContact": 0, "insuTransID": "2UP2P-HMR36-INS/9ac86b55-b5a6-43df-b975-542688c1b1f2", "fromRecNum": 4, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6828c1cd000777000000a806#266345362#2#MOBILE#VAYANT#CHANGE", "fareTypeID": 13, "channelID": 12, "bookDate": "2025-05-17T17:05:58"}]}], "payments": [{"paymentID": *********, "paxID": 266345366, "method": "IPAY", "status": "1", "paidDate": "2025-04-20T04:12:57", "cardNum": "************1361", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 602.06, "baseCurr": "EUR", "baseAmt": 602.06, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "381827", "reference": "22527007", "externalReference": "22527007", "tranId": "20900327", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "BGRMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 20900327}, {"paymentID": *********, "paxID": 266345362, "method": "VCHR", "status": "1", "paidDate": "2025-05-17T17:05:55", "voucherNum": 3261578, "paidCurr": "EUR", "paidAmt": -32.55, "baseCurr": "EUR", "baseAmt": -32.55, "userID": "MOBILE_APP", "channelID": 12, "tierID": "3", "voucherNumFull": "WDH2M9", "reference": "Refund To Voucher", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "resExternalPaymentID": 1}, {"paymentID": 209063464, "paxID": 269006176, "method": "IPAY", "status": "1", "paidDate": "2025-05-14T20:36:52", "cardNum": "************2241", "gateway": "EPS", "paidCurr": "EUR", "paidAmt": 0.42, "baseCurr": "EUR", "baseAmt": 0.42, "userID": "MOBILE_APP", "channelID": 12, "cardHolderName": "<PERSON><PERSON><PERSON>", "authCode": "250671", "reference": "23037517", "externalReference": "23037517", "tranId": "21413799", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "BGRMPGSPEEUR", "exchangeRate": "1", "resExternalPaymentID": 21413799}, {"paymentID": 208028455, "paxID": 266345362, "method": "VCHR", "status": "1", "paidDate": "2025-05-06T05:33:13", "voucherNum": 3255695, "paidCurr": "EUR", "paidAmt": -0.4, "baseCurr": "EUR", "baseAmt": -0.4, "userID": "MOBILE_APP", "channelID": 12, "tierID": "3", "voucherNumFull": "5LSC15", "reference": "Refund To Voucher", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "resExternalPaymentID": 1}], "OAFlights": null, "physicalFlights": [{"key": "16087926:181676:2025-05-16T03:25:00 PM", "LFID": 16087926, "PFID": 181676, "org": "SOF", "dest": "DXB", "depDate": "2025-05-16T15:25:00", "depTime": "2025-05-16T15:25:00", "arrTime": "2025-05-16T21:25:00", "carrier": "FZ", "flightNum": "1758", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 3", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1758", "flightStatus": "CLOSED", "originMetroGroup": "SOF", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 18000, "reaccomChangeAlert": false, "originName": "Sofia", "destinationName": "Dubai International Airport", "isActive": false, "flightChangeTime": "5/16/2025 3:30:36 PM"}, {"key": "16087936:181686:2025-05-23T10:00:00 AM", "LFID": 16087936, "PFID": 181686, "org": "DXB", "dest": "SOF", "depDate": "2025-05-23T10:00:00", "depTime": "2025-05-23T10:00:00", "arrTime": "2025-05-23T14:25:00", "carrier": "FZ", "flightNum": "1757", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1757", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "SOF", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Sofia", "isActive": false}, {"key": "16087936:181686:2025-05-25T10:00:00 AM", "LFID": 16087936, "PFID": 181686, "org": "DXB", "dest": "SOF", "depDate": "2025-05-25T10:00:00", "depTime": "2025-05-25T10:00:00", "arrTime": "2025-05-25T14:25:00", "carrier": "FZ", "flightNum": "1757", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1757", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "SOF", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Sofia", "isActive": false}, {"key": "16087936:181686:2025-05-28T10:00:00 AM", "LFID": 16087936, "PFID": 181686, "org": "DXB", "dest": "SOF", "depDate": "2025-05-28T10:00:00", "depTime": "2025-05-28T10:00:00", "arrTime": "2025-05-28T14:25:00", "carrier": "FZ", "flightNum": "1757", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1757", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "SOF", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Sofia", "isActive": true}, {"key": "16087936:181686:2025-05-30T10:00:00 AM", "LFID": 16087936, "PFID": 181686, "org": "DXB", "dest": "SOF", "depDate": "2025-05-30T10:00:00", "depTime": "2025-05-30T10:00:00", "arrTime": "2025-05-30T14:25:00", "carrier": "FZ", "flightNum": "1757", "depTerminal": "Terminal 3", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1757", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "SOF", "operatingCarrier": "FZ", "flightDuration": 19500, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Sofia", "isActive": true}], "chargeInfos": [{"recNum": 5, "charges": [{"chargeID": 1350503198, "codeType": "INSU", "amt": 8.71, "curr": "EUR", "originalAmt": 8.71, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-17T17:05:58", "desc": "INSU", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350503198:*********", "paymentID": *********, "amt": 8.71, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"1.12\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-17T17:05:58"}, {"chargeID": 1350502701, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1350502700, "amt": 11, "curr": "EUR", "originalAmt": 11, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350502701:*********", "paymentID": *********, "amt": 11, "approveCode": 0}]}, {"chargeID": 1350502704, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1350502700, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350502704:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1350502703, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1350502700, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350502703:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1350502705, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1350502700, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350502705:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1350502702, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1350502700, "amt": 18.34, "curr": "EUR", "originalAmt": 18.34, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1350502702:*********", "paymentID": *********, "amt": 18.34, "approveCode": 0}]}, {"chargeID": 1350502700, "codeType": "AIR", "amt": 176, "curr": "EUR", "originalAmt": 176, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-17T17:05:58", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 600, "tierPoints": 600, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1350502700:*********", "paymentID": *********, "amt": 176, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1353534542, "codeType": "SPST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-20T06:23:52", "desc": "SPST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181686", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181686"}, {"chargeID": 1356564069, "codeType": "SPST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-21T21:48:18", "desc": "SPST", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1356563174, "paymentMap": [], "PFID": "181686"}, {"chargeID": 1356564077, "codeType": "SPST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-21T21:48:18", "desc": "SPST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181686", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181686", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1356563174, "codeType": "SPST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-21T21:45:08", "desc": "SPST", "comment": "FLXID:SPST_ZONE1_WIN_AIS::181686", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181686"}, {"chargeID": 1356563094, "codeType": "SPST", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-21T21:45:08", "desc": "SPST", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1353534542, "paymentMap": [], "PFID": "181686"}, {"chargeID": 1350502707, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1350502700, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1350502706, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1350502700, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1350502861, "codeType": "FPML", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-17T17:05:58", "desc": "FPML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181686", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1358227460, "codeType": "CKIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-23T03:16:43", "reasonID": 12, "channelID": 25, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181686", "ssrCommentId": "*********"}]}, {"recNum": 1, "charges": [{"chargeID": 1312342783, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1312342779, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342783:*********", "paymentID": *********, "amt": 75, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-20T04:12:52"}, {"chargeID": 1312342781, "codeType": "TAX", "taxID": 12652, "taxCode": "BG", "taxChargeID": 1312342779, "amt": 11.37, "curr": "EUR", "originalAmt": 11.37, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Passenger Charge (International)", "comment": "Passenger Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342781:*********", "paymentID": *********, "amt": 11.37, "approveCode": 0}]}, {"chargeID": 1312342780, "codeType": "TAX", "taxID": 12650, "taxCode": "ZF", "taxChargeID": 1312342779, "amt": 6.8, "curr": "EUR", "originalAmt": 6.8, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Security Charge (International)", "comment": "Security Charge (International)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342780:*********", "paymentID": *********, "amt": 6.8, "approveCode": 0}]}, {"chargeID": 1312342782, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1312342779, "amt": 1.2, "curr": "EUR", "originalAmt": 1.2, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342782:*********", "paymentID": *********, "amt": 1.2, "approveCode": 0}]}, {"chargeID": 1312342779, "codeType": "AIR", "amt": 121, "curr": "EUR", "originalAmt": 121, "originalCurr": "EUR", "status": 1, "billDate": "2025-04-20T04:12:53", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 600, "tierPoints": 600, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1312342779:*********", "paymentID": *********, "amt": 121, "approveCode": 0}], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1312343041, "codeType": "PMNT", "amt": 17.54, "curr": "EUR", "originalAmt": 17.54, "originalCurr": "EUR", "status": 1, "billDate": "2025-04-20T04:13:06", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312343041:*********", "paymentID": *********, "amt": 17.54, "approveCode": 0}]}, {"chargeID": 1346641790, "codeType": "PMNT", "amt": 0.01, "curr": "EUR", "originalAmt": 0.01, "originalCurr": "EUR", "status": 1, "billDate": "2025-05-14T20:36:57", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346641790:209063464", "paymentID": 209063464, "amt": 0.01, "approveCode": 0}]}, {"chargeID": 1312342797, "codeType": "XLGR", "amt": 45.1, "curr": "EUR", "originalAmt": 45.1, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181676", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [{"key": "1312342797:*********", "paymentID": *********, "amt": 45.1, "approveCode": 0}], "PFID": "181676", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1312342785, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1312342779, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1312342784, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1312342779, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}, {"chargeID": 1312342800, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181676"}, {"chargeID": 1346466138, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T17:42:35", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312342800, "paymentMap": [], "PFID": "181676"}, {"chargeID": 1346466276, "codeType": "FPML", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 1, "exchRate": 1, "billDate": "2025-05-14T17:42:35", "desc": "FPML", "comment": "Overridden from WEB EUR 0 ", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "PromoRewards": 0, "PromoTier": 0, "paymentMap": [], "PFID": "181676", "bonusMiles": 0, "bonusTierMiles": 0, "promoMiles": 0, "promoTierMiles": 0}]}, {"recNum": 2, "charges": [{"chargeID": 1312342791, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1312342787, "amt": 10.78, "curr": "EUR", "originalAmt": 10.78, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342791:*********", "paymentID": *********, "amt": 10.78, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-04-20T04:12:52"}, {"chargeID": 1312342789, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1312342787, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342789:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1312342792, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1312342787, "amt": 1.2, "curr": "EUR", "originalAmt": 1.2, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342792:*********", "paymentID": *********, "amt": 1.2, "approveCode": 0}]}, {"chargeID": 1312342790, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1312342787, "amt": 17.97, "curr": "EUR", "originalAmt": 17.97, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342790:*********", "paymentID": *********, "amt": 17.97, "approveCode": 0}]}, {"chargeID": 1312342788, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1312342787, "amt": 1.2, "curr": "EUR", "originalAmt": 1.2, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342788:*********", "paymentID": *********, "amt": 1.2, "approveCode": 0}]}, {"chargeID": 1333617427, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1312342787, "amt": -75, "curr": "EUR", "originalAmt": -75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:15", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312342789, "paymentMap": [{"key": "1333617427:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1333617425, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1312342787, "amt": -1.2, "curr": "EUR", "originalAmt": -1.2, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:15", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312342788, "paymentMap": [{"key": "1333617425:*********", "paymentID": *********, "amt": -1.2, "approveCode": 0}]}, {"chargeID": 1333617428, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1312342787, "amt": -1.2, "curr": "EUR", "originalAmt": -1.2, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:15", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312342792, "paymentMap": [{"key": "1333617428:*********", "paymentID": *********, "amt": -1.2, "approveCode": 0}]}, {"chargeID": 1333617423, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1312342787, "amt": -10.78, "curr": "EUR", "originalAmt": -10.78, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:15", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312342791, "paymentMap": [{"key": "1333617423:*********", "paymentID": *********, "amt": -10.78, "approveCode": 0}]}, {"chargeID": 1333617424, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1312342787, "amt": -17.97, "curr": "EUR", "originalAmt": -17.97, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312342790, "paymentMap": [{"key": "1333617424:*********", "paymentID": *********, "amt": -17.97, "approveCode": 0}]}, {"chargeID": 1312342787, "codeType": "AIR", "amt": 170, "curr": "EUR", "originalAmt": 170, "originalCurr": "EUR", "status": 0, "billDate": "2025-04-20T04:12:53", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342787:*********", "paymentID": *********, "amt": 170, "approveCode": 0}]}, {"chargeID": 1333617449, "codeType": "AIR", "amt": -170, "curr": "EUR", "originalAmt": -170, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-06T05:33:15", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312342787, "paymentMap": [{"key": "1333617449:*********", "paymentID": *********, "amt": -169.6, "approveCode": 0}, {"key": "1333617449:208028455", "paymentID": 208028455, "amt": -0.4, "approveCode": 0}]}, {"chargeID": 1312342799, "codeType": "XLGR", "amt": 47.9, "curr": "EUR", "originalAmt": 47.9, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181686", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1312342799:*********", "paymentID": *********, "amt": 47.9, "approveCode": 0}], "PFID": "181686"}, {"chargeID": 1333617426, "codeType": "XLGR", "amt": -47.9, "curr": "EUR", "originalAmt": -47.9, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1312342799, "paymentMap": [{"key": "1333617426:*********", "paymentID": *********, "amt": -47.9, "approveCode": 0}], "PFID": "181686"}, {"chargeID": 1312342794, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1312342787, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1312342793, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1312342787, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1312342801, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-04-20T04:12:53", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181686"}]}, {"recNum": 3, "charges": [{"chargeID": 1333617447, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1333617442, "amt": 1.21, "curr": "EUR", "originalAmt": 1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333617447:*********", "paymentID": *********, "amt": 1.21, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-06T05:33:16"}, {"chargeID": 1333617443, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1333617442, "amt": 1.21, "curr": "EUR", "originalAmt": 1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333617443:*********", "paymentID": *********, "amt": 1.21, "approveCode": 0}]}, {"chargeID": 1333617445, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1333617442, "amt": 18.08, "curr": "EUR", "originalAmt": 18.08, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333617445:*********", "paymentID": *********, "amt": 18.08, "approveCode": 0}]}, {"chargeID": 1333617446, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1333617442, "amt": 10.85, "curr": "EUR", "originalAmt": 10.85, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333617446:*********", "paymentID": *********, "amt": 10.85, "approveCode": 0}]}, {"chargeID": 1333617444, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1333617442, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333617444:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1346641063, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1333617442, "amt": -1.21, "curr": "EUR", "originalAmt": -1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333617447, "paymentMap": [{"key": "1346641063:*********", "paymentID": *********, "amt": -1.21, "approveCode": 0}]}, {"chargeID": 1346641064, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1333617442, "amt": -1.21, "curr": "EUR", "originalAmt": -1.21, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333617443, "paymentMap": [{"key": "1346641064:*********", "paymentID": *********, "amt": -1.21, "approveCode": 0}]}, {"chargeID": 1346641067, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1333617442, "amt": -18.08, "curr": "EUR", "originalAmt": -18.08, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333617445, "paymentMap": [{"key": "1346641067:*********", "paymentID": *********, "amt": -18.08, "approveCode": 0}]}, {"chargeID": 1346641069, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1333617442, "amt": -75, "curr": "EUR", "originalAmt": -75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333617444, "paymentMap": [{"key": "1346641069:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1346641068, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1333617442, "amt": -10.85, "curr": "EUR", "originalAmt": -10.85, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333617446, "paymentMap": [{"key": "1346641068:*********", "paymentID": *********, "amt": -10.85, "approveCode": 0}]}, {"chargeID": 1333617442, "codeType": "AIR", "amt": 172, "curr": "EUR", "originalAmt": 172, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-06T05:33:16", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333617442:*********", "paymentID": *********, "amt": 172, "approveCode": 0}]}, {"chargeID": 1346641065, "codeType": "AIR", "amt": -172, "curr": "EUR", "originalAmt": -172, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-14T20:36:46", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333617442, "paymentMap": [{"key": "1346641065:*********", "paymentID": *********, "amt": -172, "approveCode": 0}]}, {"chargeID": 1333617693, "codeType": "XLGR", "amt": 45.3, "curr": "EUR", "originalAmt": 45.3, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-06T05:33:16", "desc": "XLGR", "comment": "FLXID:XLGR_73B_ZONE3_WIN_AIS::181686", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333617693:*********", "paymentID": *********, "amt": 45.3, "approveCode": 0}], "PFID": "181686"}, {"chargeID": 1346641066, "codeType": "XLGR", "amt": -45.3, "curr": "EUR", "originalAmt": -45.3, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-14T20:36:46", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333617693, "paymentMap": [{"key": "1346641066:*********", "paymentID": *********, "amt": -45.3, "approveCode": 0}], "PFID": "181686"}, {"chargeID": 1333617469, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1333617442, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1333617448, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1333617442, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346466120, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T17:42:35", "desc": "Standard meal", "comment": "Cancel SSR Refund To Voucher Flyer", "reasonID": 7, "channelID": 5, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333617475, "paymentMap": [], "PFID": "181686"}, {"chargeID": 1333617475, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T05:33:16", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181686"}, {"chargeID": 1346466277, "codeType": "FPML", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-14T17:42:35", "desc": "FPML", "comment": "Overridden from WEB EUR 0 ", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181686"}]}, {"recNum": 4, "charges": [{"chargeID": 1346641071, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346641070, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346641071:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}], "isSSR": false, "ChargeBookDate": "2025-05-14T20:36:46"}, {"chargeID": 1346641074, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1346641070, "amt": 11, "curr": "EUR", "originalAmt": 11, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346641074:*********", "paymentID": *********, "amt": 11, "approveCode": 0}]}, {"chargeID": 1346641072, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346641070, "amt": 75, "curr": "EUR", "originalAmt": 75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346641072:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1346641075, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1346641070, "amt": 1.23, "curr": "EUR", "originalAmt": 1.23, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346641075:*********", "paymentID": *********, "amt": 1.23, "approveCode": 0}]}, {"chargeID": 1346641073, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1346641070, "amt": 18.34, "curr": "EUR", "originalAmt": 18.34, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346641073:*********", "paymentID": *********, "amt": 18.34, "approveCode": 0}]}, {"chargeID": 1350502695, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1346641070, "amt": -1.23, "curr": "EUR", "originalAmt": -1.23, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "Passengers Security & Safety Service Fees", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1346641075, "paymentMap": [{"key": "1350502695:*********", "paymentID": *********, "amt": -1.23, "approveCode": 0}]}, {"chargeID": 1350502694, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1346641070, "amt": -11, "curr": "EUR", "originalAmt": -11, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1346641074, "paymentMap": [{"key": "1350502694:*********", "paymentID": *********, "amt": -11, "approveCode": 0}]}, {"chargeID": 1350502697, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1346641070, "amt": -18.34, "curr": "EUR", "originalAmt": -18.34, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "Passenger Service Charge (Intl)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1346641073, "paymentMap": [{"key": "1350502697:*********", "paymentID": *********, "amt": -18.34, "approveCode": 0}]}, {"chargeID": 1350502693, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1346641070, "amt": -1.23, "curr": "EUR", "originalAmt": -1.23, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1346641071, "paymentMap": [{"key": "1350502693:*********", "paymentID": *********, "amt": -1.23, "approveCode": 0}]}, {"chargeID": 1350502699, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1346641070, "amt": -75, "curr": "EUR", "originalAmt": -75, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-17T17:05:58", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1346641072, "paymentMap": [{"key": "1350502699:*********", "paymentID": *********, "amt": -75, "approveCode": 0}]}, {"chargeID": 1350502696, "codeType": "AIR", "amt": -174, "curr": "EUR", "originalAmt": -174, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-17T17:05:58", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1346641070, "paymentMap": [{"key": "1350502696:*********", "paymentID": *********, "amt": -32.14, "approveCode": 0}, {"key": "1350502696:*********", "paymentID": *********, "amt": -141.86, "approveCode": 0}]}, {"chargeID": 1346641070, "codeType": "AIR", "amt": 174, "curr": "EUR", "originalAmt": 174, "originalCurr": "EUR", "status": 0, "billDate": "2025-05-14T20:36:46", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346641070:*********", "paymentID": *********, "amt": 174, "approveCode": 0}]}, {"chargeID": 1346641212, "codeType": "XLGR", "amt": 43.26, "curr": "EUR", "originalAmt": 43.26, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-14T20:36:46", "desc": "XLGR", "comment": "FLXID:XLGR_73H_EMER_ZONE3_WIN_AIS::181686", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1346641212:*********", "paymentID": *********, "amt": 42.85, "approveCode": 0}, {"key": "1346641212:209063464", "paymentID": 209063464, "amt": 0.41, "approveCode": 0}], "PFID": "181686"}, {"chargeID": 1350502698, "codeType": "XLGR", "amt": -43.26, "curr": "EUR", "originalAmt": -43.26, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-17T17:05:58", "desc": "XLGR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1346641212, "paymentMap": [{"key": "1350502698:*********", "paymentID": *********, "amt": -0.41, "approveCode": 0}, {"key": "1350502698:*********", "paymentID": *********, "amt": -42.85, "approveCode": 0}], "PFID": "181686"}, {"chargeID": 1346641077, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1346641070, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "Included seat", "comment": "Included seat", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346641076, "codeType": "BAGL", "taxID": 8947, "taxCode": "BAGL", "taxChargeID": 1346641070, "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 0, "billDate": "2025-05-14T20:36:46", "desc": "30kg BAG INCLUDED IN FARE", "comment": "30kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1346640884, "codeType": "FPML", "amt": 0, "curr": "EUR", "originalAmt": 0, "originalCurr": "EUR", "status": 0, "exchRate": 1, "billDate": "2025-05-14T20:36:46", "desc": "FPML", "comment": "FLXID:0 AED-Fare brand rule:", "reasonID": 0, "channelID": 12, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181686"}]}], "parentPNRs": [], "childPNRs": []}