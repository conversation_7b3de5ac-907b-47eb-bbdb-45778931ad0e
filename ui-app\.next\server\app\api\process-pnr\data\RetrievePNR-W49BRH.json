{"seriesNum": "299", "PNR": "W49BRH", "bookAgent": "WEB2_LIVE", "resCurrency": "QAR", "PNRPin": "82184260", "bookDate": "2025-04-21T14:04:25", "modifyDate": "2025-05-10T19:00:53", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "dcc68518q4w5x8utb15264vbr2fb97b09034d2a4d7e8", "securityGUID": "dcc68518q4w5x8utb15264vbr2fb97b09034d2a4d7e8", "lastLoadGUID": "9b762de1-c241-4802-ac4e-3d3f8b434cb6", "MasterPNR": "W49BRH", "segments": [{"segKey": "16205249:16205249:5/11/2025 5:05:00 PM", "LFID": 16205249, "depDate": "2025-05-11T00:00:00", "flightGroupId": "16205249", "org": "DOH", "dest": "WAW", "depTime": "2025-05-11T17:05:00", "depTimeGMT": "2025-05-11T14:05:00", "arrTime": "2025-05-12T08:30:00", "operCarrier": "FZ", "operFlightNum": "018/1835", "mrktCarrier": "FZ ", "mrktFlightNum": "018/1835", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181022, "depDate": "2025-05-11T17:05:00", "legKey": "16205249:181022:5/11/2025 5:05:00 PM", "customerKey": "04371964626174CFEB1FF0A7A8F9AA43A0DE6212180CA6371F6C6C7372C11A91"}, {"PFID": 182265, "depDate": "2025-05-12T03:55:00", "legKey": "16205249:182265:5/12/2025 3:55:00 AM", "customerKey": "17319EDEAE3278FAF921E868D6E0C83C59777E92729AFDD3DFA38C7BDEECF66F"}], "active": true, "changeType": "TK"}, {"segKey": "16205244:16205244:5/7/2025 8:50:00 PM", "LFID": 16205244, "depDate": "2025-05-07T00:00:00", "flightGroupId": "16205244", "org": "DOH", "dest": "WAW", "depTime": "2025-05-07T20:50:00", "depTimeGMT": "2025-05-07T17:50:00", "arrTime": "2025-05-08T08:20:00", "operCarrier": "FZ", "operFlightNum": "006/1835", "mrktCarrier": "FZ ", "mrktFlightNum": "006/1835", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 181013, "depDate": "2025-05-07T20:50:00", "legKey": "16205244:181013:5/7/2025 8:50:00 PM", "customerKey": "FBB2DDB205DDE2B58BA66D1784D07306E86B7108CCD59B42312FE48232AD2B62"}, {"PFID": 182265, "depDate": "2025-05-08T03:50:00", "legKey": "16205244:182265:5/8/2025 3:50:00 AM", "customerKey": "C7117287F01E4833F8BC4EEF26E41460D08BC548BBE5BDC624982F334C92D5B5"}], "active": true, "changeType": "TK"}], "persons": [{"paxID": 266495976, "fName": "VERONIKA", "lName": "AKSONOVA", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "2005-05-31T00:00:00", "recNum": [1, 2], "nameChangeCount": "1"}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "N", "insuPurchasedate": "4/21/2025 2:03:25 PM", "provider": "AIG", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "986605873", "insuTransID": "630514af-6b9b-4d40-83df-8722e8bffc65", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68064f76000777000001260f#1#1#WEB#VAYANT#CREATE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-04-21T14:04:25"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "N", "status": 0, "fareClass": "N", "operFareClass": "N", "FBC": "NOB7QA2", "fareBrand": "Value", "cabin": "ECONOMY", "insuTransID": "BSGCH-XP6WB-INS/90cea89e-1d27-4d13-a58a-b89bc241e2d1", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "681a1e9a0007780000000feb#266495976#1#WEB#OneSearch#CHANGE", "fareTypeID": 12, "channelID": 2, "cancelReasonID": 4, "bookDate": "2025-05-06T14:39:06"}]}], "payments": [{"paymentID": *********, "paxID": 266496096, "method": "MSCD", "status": "1", "paidDate": "2025-04-21T14:05:02", "cardNum": "************3323", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 1475.99, "baseCurr": "QAR", "baseAmt": 1475.99, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "<PERSON><PERSON>", "authCode": "015766", "reference": "22555550", "externalReference": "22555550", "tranId": "20931370", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 20931370}, {"paymentID": *********, "paxID": 266495976, "method": "VCHR", "status": "1", "paidDate": "2025-05-10T19:00:51", "voucherNum": 3258409, "paidCurr": "QAR", "paidAmt": -200, "baseCurr": "QAR", "baseAmt": -200, "userID": "WEB2_LIVE", "channelID": 2, "voucherNumFull": "VNBLMM", "reference": "Refund To Voucher", "tranId": "1", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "resExternalPaymentID": 1}, {"paymentID": *********, "paxID": 268096800, "method": "MSCD", "status": "1", "paidDate": "2025-05-06T15:27:08", "cardNum": "************3323", "gateway": "EPS", "paidCurr": "QAR", "paidAmt": 200.22, "baseCurr": "QAR", "baseAmt": 200.22, "userID": "paybylink", "channelID": 2, "cardHolderName": "Saltanat Nurkulova", "authCode": "065534", "reference": "22876697", "externalReference": "22876697", "tranId": "21249038", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "FZMPGSPEQAR", "exchangeRate": "1", "resExternalPaymentID": 21249038}], "OAFlights": null, "physicalFlights": [{"key": "16205244:181013:2025-05-07T08:50:00 PM", "LFID": 16205244, "PFID": 181013, "org": "DOH", "dest": "DXB", "depDate": "2025-05-07T20:50:00", "depTime": "2025-05-07T20:50:00", "arrTime": "2025-05-07T23:05:00", "carrier": "FZ", "flightNum": "006", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "006", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "12/19/2024 8:44:35 PM"}, {"key": "16205244:182265:2025-05-08T03:50:00 AM", "LFID": 16205244, "PFID": 182265, "org": "DXB", "dest": "WAW", "depDate": "2025-05-08T03:50:00", "depTime": "2025-05-08T03:50:00", "arrTime": "2025-05-08T08:20:00", "carrier": "FZ", "flightNum": "1835", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1835", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "WAW", "operatingCarrier": "FZ", "flightDuration": 23400, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Warsaw", "isActive": false, "changeType": "TK", "flightChangeTime": "12/19/2024 8:44:35 PM"}, {"key": "16205249:181022:2025-05-11T05:05:00 PM", "LFID": 16205249, "PFID": 181022, "org": "DOH", "dest": "DXB", "depDate": "2025-05-11T17:05:00", "depTime": "2025-05-11T17:05:00", "arrTime": "2025-05-11T19:20:00", "carrier": "FZ", "flightNum": "018", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "018", "flightStatus": "CLOSED", "originMetroGroup": "DOH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Doha", "destinationName": "Dubai International Airport", "isActive": false, "changeType": "TK", "flightChangeTime": "5/7/2025 6:40:00 AM"}, {"key": "16205249:182265:2025-05-12T03:55:00 AM", "LFID": 16205249, "PFID": 182265, "org": "DXB", "dest": "WAW", "depDate": "2025-05-12T03:55:00", "depTime": "2025-05-12T03:55:00", "arrTime": "2025-05-12T08:30:00", "carrier": "FZ", "flightNum": "1835", "depTerminal": "Terminal 3", "flightOrder": 2, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "1835", "flightStatus": "CLOSED", "originMetroGroup": "DXB", "destinationMetroGroup": "WAW", "operatingCarrier": "FZ", "flightDuration": 23700, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Warsaw", "isActive": false, "changeType": "TK", "flightChangeTime": "5/7/2025 6:40:00 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 33, "curr": "QAR", "originalAmt": 33, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-04-21T14:04:25", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 33, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-04-21T14:04:25"}, {"chargeID": **********, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1314254199, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1314254199:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1314254198, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1314254198:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1314254194, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1314254194:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1314254197, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1314254197:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1314254195, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1314254195:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1334593248, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1334593248:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1334593245, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Passenger Service Charge", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1314254197, "paymentMap": [{"key": "1334593245:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1334593289, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Passenger Facility Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1314254194, "paymentMap": [{"key": "1334593289:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1334593244, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1314254199, "paymentMap": [{"key": "1334593244:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1334593290, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": **********, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Airport Fee.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1314254195, "paymentMap": [{"key": "1334593290:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1334593247, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1314254198, "paymentMap": [{"key": "1334593247:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1334593246, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -190, "curr": "QAR", "originalAmt": -190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1334593246:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 1010, "curr": "QAR", "originalAmt": 1010, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-21T14:04:25", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 1010, "approveCode": 0}]}, {"chargeID": 1334593243, "codeType": "AIR", "amt": -1010, "curr": "QAR", "originalAmt": -1010, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-06T14:39:07", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1334593243:*********", "paymentID": *********, "amt": -1010, "approveCode": 0}]}, {"chargeID": 1314258797, "codeType": "PMNT", "amt": 42.99, "curr": "QAR", "originalAmt": 42.99, "originalCurr": "QAR", "status": 0, "billDate": "2025-04-21T14:05:06", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1314258797:*********", "paymentID": *********, "amt": 42.99, "approveCode": 0}]}, {"chargeID": 1334593291, "codeType": "PNLT", "amt": 149, "curr": "QAR", "originalAmt": 149, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-06T14:39:07", "desc": "Penalty AddedDueToModify FZ  006 DOH  - DXB  07-May-2025", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593291:*********", "paymentID": *********, "amt": 149, "approveCode": 0}]}, {"chargeID": 1314254201, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": **********, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1314254266, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "182265"}, {"chargeID": 1314254265, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-04-21T14:04:25", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181013"}]}, {"recNum": 2, "charges": [{"chargeID": 1334593367, "codeType": "INSU", "amt": 35.39, "curr": "QAR", "originalAmt": 35.39, "originalCurr": "QAR", "status": 0, "exchRate": 1, "billDate": "2025-05-06T14:39:07", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593367:*********", "paymentID": *********, "amt": 35.39, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"0.27\",\"Premium\":\"9.72\",\"Tax\":\"0.46\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-06T14:39:07"}, {"chargeID": 1340502112, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1334593287, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T19:00:52", "desc": "Advanced passenger information fee", "comment": "Cancel Refund As Paid", "reasonID": 4, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334593333, "paymentMap": [{"key": "1340502112:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1340502113, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1334593287, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T19:00:52", "desc": "Airport Fee.", "comment": "Cancel Refund As Paid", "reasonID": 4, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334593329, "paymentMap": [{"key": "1340502113:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1340502116, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1334593287, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T19:00:52", "desc": "Passenger Service Charge", "comment": "Cancel Refund As Paid", "reasonID": 4, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334593331, "paymentMap": [{"key": "1340502116:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1340502109, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1334593287, "amt": -190, "curr": "QAR", "originalAmt": -190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T19:00:52", "desc": "YQ - DUMMY", "comment": "Cancel Refund As Paid", "reasonID": 4, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334593334, "paymentMap": [{"key": "1340502109:*********", "paymentID": *********, "amt": -190, "approveCode": 0}]}, {"chargeID": 1340502111, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1334593287, "amt": -60, "curr": "QAR", "originalAmt": -60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T19:00:52", "desc": "Passenger Facility Charge.", "comment": "Cancel Refund As Paid", "reasonID": 4, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334593288, "paymentMap": [{"key": "1340502111:*********", "paymentID": *********, "amt": -60, "approveCode": 0}]}, {"chargeID": 1340502110, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1334593287, "amt": -10, "curr": "QAR", "originalAmt": -10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T19:00:52", "desc": "Passenger safety and security fees (PSSF)", "comment": "Cancel Refund As Paid", "reasonID": 4, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334593330, "paymentMap": [{"key": "1340502110:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1340502114, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1334593287, "amt": -50, "curr": "QAR", "originalAmt": -50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-10T19:00:52", "desc": "Passenger Facilities Charge.", "comment": "Cancel Refund As Paid", "reasonID": 4, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334593332, "paymentMap": [{"key": "1340502114:*********", "paymentID": *********, "amt": -50, "approveCode": 0}]}, {"chargeID": 1334593329, "codeType": "TAX", "taxID": 13810, "taxCode": "QA", "taxChargeID": 1334593287, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Airport Fee.", "comment": "Airport Fee.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593329:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1334593331, "codeType": "TAX", "taxID": 2186, "taxCode": "PZ", "taxChargeID": 1334593287, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Passenger Service Charge", "comment": "Passenger Service Charge", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593331:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1334593333, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1334593287, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593333:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1334593332, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1334593287, "amt": 50, "curr": "QAR", "originalAmt": 50, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593332:*********", "paymentID": *********, "amt": 50, "approveCode": 0}]}, {"chargeID": 1334593288, "codeType": "TAX", "taxID": 13811, "taxCode": "G4", "taxChargeID": 1334593287, "amt": 60, "curr": "QAR", "originalAmt": 60, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Passenger Facility Charge.", "comment": "Passenger Facility Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593288:*********", "paymentID": *********, "amt": 60, "approveCode": 0}]}, {"chargeID": 1334593330, "codeType": "TAX", "taxID": 13230, "taxCode": "R9", "taxChargeID": 1334593287, "amt": 10, "curr": "QAR", "originalAmt": 10, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Passenger safety and security fees (PSSF)", "comment": "Passenger safety and security fees (PSSF)", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593330:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": 1334593334, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1334593287, "amt": 190, "curr": "QAR", "originalAmt": 190, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593334:*********", "paymentID": *********, "amt": 190, "approveCode": 0}]}, {"chargeID": 1340502115, "codeType": "AIR", "amt": -1020, "curr": "QAR", "originalAmt": -1020, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-10T19:00:52", "desc": "WEB:AIR", "comment": "Cancel Refund As Paid", "reasonID": 4, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1334593287, "paymentMap": [{"key": "1340502115:*********", "paymentID": *********, "amt": -200, "approveCode": 0}, {"key": "1340502115:*********", "paymentID": *********, "amt": -820, "approveCode": 0}]}, {"chargeID": 1334593287, "codeType": "AIR", "amt": 1020, "curr": "QAR", "originalAmt": 1020, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-06T14:39:07", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334593287:*********", "paymentID": *********, "amt": 159, "approveCode": 0}, {"key": "1334593287:*********", "paymentID": *********, "amt": 861, "approveCode": 0}]}, {"chargeID": 1334665776, "codeType": "PMNT", "amt": 5.83, "curr": "QAR", "originalAmt": 5.83, "originalCurr": "QAR", "status": 0, "billDate": "2025-05-06T15:27:13", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1334665776:*********", "paymentID": *********, "amt": 5.83, "approveCode": 0}]}, {"chargeID": 1340502117, "codeType": "PNLT", "amt": 1210, "curr": "QAR", "originalAmt": 1210, "originalCurr": "QAR", "status": 1, "billDate": "2025-05-10T19:00:52", "desc": "Penalty RefundAsPaid FZ  018 DOH  - DXB  11-May-2025", "comment": "Penalty Refund As Paid", "reasonID": 4, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1340502117:*********", "paymentID": *********, "amt": 1210, "approveCode": 0}]}, {"chargeID": 1334593335, "codeType": "BAGB", "taxID": 8946, "taxCode": "BAGB", "taxChargeID": 1334593287, "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "20kg BAG INCLUDED IN FARE", "comment": "20kg BAG INCLUDED IN FARE", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1334593342, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "182265"}, {"chargeID": 1334593341, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "QAR", "originalAmt": 0, "originalCurr": "QAR", "status": 0, "exchRate": 0, "billDate": "2025-05-06T14:39:07", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181022"}]}], "parentPNRs": [], "childPNRs": []}