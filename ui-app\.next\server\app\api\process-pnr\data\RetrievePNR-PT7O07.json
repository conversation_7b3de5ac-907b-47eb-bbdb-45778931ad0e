{"seriesNum": "299", "PNR": "PT7O07", "bookAgent": "WEB2_LIVE", "resCurrency": "AED", "PNRPin": "82958054", "bookDate": "2025-05-19T14:53:37", "modifyDate": "2025-05-19T15:00:36", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 1, "activeSegCount": 2, "webBookingID": "6afed1477a7a04wyx8obu24bdb6er3fe4a677775c53e", "securityGUID": "6afed1477a7a04wyx8obu24bdb6er3fe4a677775c53e", "lastLoadGUID": "357A8DBA6DA64CEBE0631F206F0A7F93", "isAsyncPNR": false, "MasterPNR": "PT7O07", "segments": [{"segKey": "16087410:16087410:6/21/2025 10:20:00 AM", "LFID": 16087410, "depDate": "2025-06-21T00:00:00", "flightGroupId": "16087410", "org": "DXB", "dest": "CMB", "depTime": "2025-06-21T10:20:00", "depTimeGMT": "2025-06-21T06:20:00", "arrTime": "2025-06-21T16:35:00", "operCarrier": "FZ", "operFlightNum": "549", "mrktCarrier": "FZ ", "mrktFlightNum": "549", "persons": [{"recNum": 1, "status": 1}], "legDetails": [{"PFID": 181171, "depDate": "2025-06-21T10:20:00", "legKey": "16087410:181171:6/21/2025 10:20:00 AM", "customerKey": "450DAB3B98430BB295FEAE655563BBDE5FE8287CA8578D4F667F40BF106C12D2"}], "active": true, "changeType": "AC"}, {"segKey": "16087399:16087399:7/21/2025 5:35:00 PM", "LFID": 16087399, "depDate": "2025-07-21T00:00:00", "flightGroupId": "16087399", "org": "CMB", "dest": "DXB", "depTime": "2025-07-21T17:35:00", "depTimeGMT": "2025-07-21T12:05:00", "arrTime": "2025-07-21T20:45:00", "operCarrier": "FZ", "operFlightNum": "550", "mrktCarrier": "FZ ", "mrktFlightNum": "550", "persons": [{"recNum": 2, "status": 1}], "legDetails": [{"PFID": 181150, "depDate": "2025-07-21T17:35:00", "legKey": "16087399:181150:7/21/2025 5:35:00 PM", "customerKey": "0555650F17991D6649E1068253DB2982B8A38F054DC4728FCD0ECFB055D9E2C5"}], "active": true, "changeType": "AC"}], "persons": [{"paxID": 269484204, "fName": "PANAGODA LIYANA ARACHCHILAGE", "lName": "NIMANTHIE SHAMALI PANAGODA", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "1978-06-25T00:00:00", "nationality": "144", "recNum": [1, 2]}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/19/2025 2:53:37 PM", "provider": "<PERSON>", "status": 1, "fareClass": "K", "operFareClass": "K", "FBC": "KRX8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "6AN6G-CHNPL-INS/bd286aa9-05f3-441a-ac1b-86280ed4394c", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682b44cf0007780000003d35#1#1#WEB#VAYANT#CREATE", "fareTypeID": 13, "channelID": 2, "bookDate": "2025-05-19T14:53:37"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "markFareClass": "K", "insuPurchasedate": "5/19/2025 2:53:37 PM", "provider": "<PERSON>", "status": 1, "fareClass": "K", "operFareClass": "K", "FBC": "KRX8AE5", "fareBrand": "Flex", "cabin": "ECONOMY", "insuTransID": "6AN6G-CHNPL-INS/bd286aa9-05f3-441a-ac1b-86280ed4394c", "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "682b44cf0007780000003d35#1#2#WEB#VAYANT#CREATE", "fareTypeID": 13, "channelID": 2, "bookDate": "2025-05-19T14:53:37"}]}], "payments": [{"paymentID": *********, "paxID": 269484670, "method": "VISA", "status": "1", "paidDate": "2025-05-19T14:57:38", "cardNum": "************9604", "gateway": "EPS", "paidCurr": "AED", "paidAmt": 1736.27, "baseCurr": "AED", "baseAmt": 1736.27, "userID": "WEB2_LIVE", "channelID": 2, "cardHolderName": "ABIR MOUSSA", "authCode": "958257", "reference": "23128780", "externalReference": "23128780", "tranId": "21504044", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "visapgallmotoaedallniall001", "exchangeRate": "1", "resExternalPaymentID": 21504044}], "OAFlights": null, "physicalFlights": [{"key": "16087410:181171:2025-06-21T10:20:00 AM", "LFID": 16087410, "PFID": 181171, "org": "DXB", "dest": "CMB", "depDate": "2025-06-21T10:20:00", "depTime": "2025-06-21T10:20:00", "arrTime": "2025-06-21T16:35:00", "carrier": "FZ", "flightNum": "549", "depTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73V", "mrktCarrier": "FZ", "mrktFlightNum": "549", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "CMB", "operatingCarrier": "FZ", "flightDuration": 17100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Colombo", "isActive": true, "changeType": "AC", "flightChangeTime": "3/10/2025 6:41:30 AM"}, {"key": "16087399:181150:2025-07-21T05:35:00 PM", "LFID": 16087399, "PFID": 181150, "org": "CMB", "dest": "DXB", "depDate": "2025-07-21T17:35:00", "depTime": "2025-07-21T17:35:00", "arrTime": "2025-07-21T20:45:00", "carrier": "FZ", "flightNum": "550", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73V", "mrktCarrier": "FZ", "mrktFlightNum": "550", "flightStatus": "OPEN", "originMetroGroup": "CMB", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 16800, "reaccomChangeAlert": false, "originName": "Colombo", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "3/24/2025 8:17:26 AM"}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": 1352904936, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904936:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"38.31\",\r\n  \"Tax\": \"1.82\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-19T14:53:36"}, {"chargeID": 1352904900, "codeType": "TAX", "taxID": 1624, "taxCode": "AE", "taxChargeID": 1352904899, "amt": 75, "curr": "AED", "originalAmt": 75, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Passenger Service Charge (Intl)", "comment": "Passenger Service Charge (Intl)", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904900:*********", "paymentID": *********, "amt": 75, "approveCode": 0}]}, {"chargeID": 1352904902, "codeType": "TAX", "taxID": 4844, "taxCode": "TP", "taxChargeID": 1352904899, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Passengers Security & Safety Service Fees", "comment": "Passengers Security & Safety Service Fees", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904902:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352904901, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1352904899, "amt": 45, "curr": "AED", "originalAmt": 45, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904901:*********", "paymentID": *********, "amt": 45, "approveCode": 0}]}, {"chargeID": 1352904903, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352904899, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904903:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352904904, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352904899, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904904:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1352904899, "codeType": "AIR", "amt": 490, "curr": "AED", "originalAmt": 490, "originalCurr": "AED", "status": 1, "billDate": "2025-05-19T14:53:37", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904899:*********", "paymentID": *********, "amt": 490, "approveCode": 0}]}, {"chargeID": 1352910996, "codeType": "PMNT", "amt": 50.57, "curr": "AED", "originalAmt": 50.57, "originalCurr": "AED", "status": 1, "billDate": "2025-05-19T14:57:42", "desc": "Payment Fee", "comment": "Payment Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352910996:*********", "paymentID": *********, "amt": 50.57, "approveCode": 0}]}, {"chargeID": 1352904937, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "FRST", "comment": "FLXID:NSST_ZONE1_WIN_AIS::181171", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181171"}, {"chargeID": 1352904906, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1352904899, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352904905, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1352904899, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352904941, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181171"}]}, {"recNum": 2, "charges": [{"chargeID": 1352904939, "codeType": "INSU", "amt": 70.35, "curr": "AED", "originalAmt": 70.35, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904939:*********", "paymentID": *********, "amt": 70.35, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\r\n  \"Currency\": \"USD\",\r\n  \"Fx\": \"0.27\",\r\n  \"Premium\": \"38.31\",\r\n  \"Tax\": \"1.82\",\r\n  \"SegPaxCount\": \"2\"\r\n}", "ChargeBookDate": "2025-05-19T14:53:36"}, {"chargeID": 1352904930, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1352904908, "amt": 100, "curr": "AED", "originalAmt": 100, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904930:*********", "paymentID": *********, "amt": 100, "approveCode": 0}]}, {"chargeID": 1352904929, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1352904908, "amt": 5, "curr": "AED", "originalAmt": 5, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904929:*********", "paymentID": *********, "amt": 5, "approveCode": 0}]}, {"chargeID": 1352904931, "codeType": "TAX", "taxID": 11666, "taxCode": "LK", "taxChargeID": 1352904908, "amt": 230, "curr": "AED", "originalAmt": 230, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Embarkation Tax", "comment": "Embarkation Tax", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904931:*********", "paymentID": *********, "amt": 230, "approveCode": 0}]}, {"chargeID": 1352904908, "codeType": "AIR", "amt": 490, "curr": "AED", "originalAmt": 490, "originalCurr": "AED", "status": 1, "billDate": "2025-05-19T14:53:37", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1352904908:*********", "paymentID": *********, "amt": 490, "approveCode": 0}]}, {"chargeID": 1352904940, "codeType": "FRST", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "FRST", "comment": "FLXID:NSST_ZONE1_WIN_AIS::181150", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181150"}, {"chargeID": 1352904933, "codeType": "INST", "taxID": 11187, "taxCode": "INST", "taxChargeID": 1352904908, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Included seat", "comment": "Included seat", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352904932, "codeType": "BAGX", "taxID": 9046, "taxCode": "BAGX", "taxChargeID": 1352904908, "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "40kg BAG INCLUDED IN FARE", "comment": "40kg BAG INCLUDED IN FARE", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": []}, {"chargeID": 1352904942, "codeType": "MLIN", "taxID": 11186, "taxCode": "MLIN", "amt": 0, "curr": "AED", "originalAmt": 0, "originalCurr": "AED", "status": 1, "exchRate": 0, "billDate": "2025-05-19T14:53:37", "desc": "Standard meal", "comment": "SSR Added", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [], "PFID": "181150"}]}], "parentPNRs": [], "childPNRs": []}