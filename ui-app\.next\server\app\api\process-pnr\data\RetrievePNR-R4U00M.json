{"seriesNum": "299", "PNR": "R4U00M", "bookAgent": "WEB2_LIVE", "resCurrency": "BHD", "PNRPin": "82565177", "bookDate": "2025-05-05T15:14:59", "modifyDate": "2025-05-13T14:31:08", "resType": "WEB", "resBalance": 0, "timeLimitOverride": 0, "isInterline": false, "isCodeShare": false, "activePaxCount": 0, "activeSegCount": 0, "webBookingID": "70f88f34e8e143a0d8q77f12l8u60fyb66ibw8400a66", "securityGUID": "70f88f34e8e143a0d8q77f12l8u60fyb66ibw8400a66", "lastLoadGUID": "3506372022CD02CDE0631E206F0A3F06", "MasterPNR": "R4U00M", "segments": [{"segKey": "16804773:16804773:7/15/2025 3:35:00 PM", "LFID": 16804773, "depDate": "2025-07-15T00:00:00", "flightGroupId": "16804773", "org": "BAH", "dest": "JED", "depTime": "2025-07-15T15:35:00", "depTimeGMT": "2025-07-15T12:35:00", "arrTime": "2025-07-15T21:55:00", "operCarrier": "FZ", "operFlightNum": "022/807", "mrktCarrier": "FZ ", "mrktFlightNum": "022/807", "persons": [{"recNum": 2, "status": 0}], "legDetails": [{"PFID": 181015, "depDate": "2025-07-15T15:35:00", "legKey": "16804773:181015:7/15/2025 3:35:00 PM", "customerKey": "378557B72FF3B9E0A3A9300393FB8F024C14204E6BFD15A9E381EF8CA2BBCE98"}, {"PFID": 185998, "depDate": "2025-07-15T19:50:00", "legKey": "16804773:185998:7/15/2025 7:50:00 PM", "customerKey": "BF205B469C56E72B7C09AB026DBF21D98A2CD726FCD96CBFD267A794E84E4E00"}], "active": true, "changeType": "AC"}, {"segKey": "16534002:16534002:1/7/2026 1:40:00 AM", "LFID": 16534002, "depDate": "2026-01-07T00:00:00", "flightGroupId": "16534002", "org": "BAH", "dest": "JED", "depTime": "2026-01-07T01:40:00", "depTimeGMT": "2026-01-06T22:40:00", "arrTime": "2026-01-07T11:45:00", "operCarrier": "FZ", "operFlightNum": "030/909", "mrktCarrier": "FZ ", "mrktFlightNum": "030/909", "persons": [{"recNum": 1, "status": 0}], "legDetails": [{"PFID": 184291, "depDate": "2026-01-07T01:40:00", "legKey": "16534002:184291:1/7/2026 1:40:00 AM", "customerKey": "CA6E1DD7B0F677C5CC838FA5AC92167797333488D56415E4D6A5DE644A1BAC02"}, {"PFID": 184595, "depDate": "2026-01-07T09:25:00", "legKey": "16534002:184595:1/7/2026 9:25:00 AM", "customerKey": "8B61436AA1B2A7F699AEE56E8EA7494F8D27C970F24B23AE9DF0C304C3B69957"}], "active": true}], "persons": [{"paxID": 267971955, "fName": "PRATIMA", "lName": "TEST", "title": "MS", "PTCID": 1, "gender": "F", "DOB": "2008-06-05T00:00:00", "nationality": "784", "recNum": [1, 2], "nameChangeCount": "1"}], "paxSegments": [{"recNum": 1, "recordDetails": [{"bookAgent": "WEB2_LIVE", "cancelAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "insuPurchasedate": "5/5/2025 3:14:11 PM", "provider": "AIG", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TO6BH2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuConfNum": "987207543", "insuTransID": "f50475f8-80d2-4654-818c-e00c5acb67f3", "toRecNum": 2, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "6818d54d0007770000000b15#1#1#WEB#VAYANT#CREATE", "fareTypeID": 11, "channelID": 2, "cancelReasonID": 0, "bookDate": "2025-05-05T15:14:59"}]}, {"recNum": 2, "recordDetails": [{"bookAgent": "WEB2_LIVE", "statusReasonID": 0, "userResponseId": -1, "markFareClass": "T", "status": 0, "fareClass": "T", "operFareClass": "T", "FBC": "TO6BH2", "fareBrand": "Lite", "cabin": "ECONOMY", "changeConsent": 0, "insuTransID": "V2K3V-L4FMY-INS/272a37b3-b782-4290-86c5-719d79785697", "fromRecNum": 1, "fareCarrier": "FZ", "primaryPax": true, "pricingKey": "68194db30007780000002301#267971955#1#WEB#OneSearch#CHANGE", "fareTypeID": 11, "channelID": 2, "cancelReasonID": 7, "bookDate": "2025-05-05T23:48:20"}]}], "payments": [{"paymentID": *********, "paxID": 267970930, "method": "VCHR", "status": "1", "paidDate": "2025-05-05T15:15:02", "voucherNum": 3255491, "gateway": "EPS", "paidCurr": "BHD", "paidAmt": 174.28, "baseCurr": "BHD", "baseAmt": 174.28, "userID": "WEB2_LIVE", "channelID": 2, "voucherNumFull": "4T89F4", "authCode": "4T89F4", "reference": "A5312485", "externalReference": "A5312485", "tranId": "21225715", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21225715}, {"paymentID": *********, "paxID": 267970601, "method": "VCHR", "status": "1", "paidDate": "2025-05-06T00:05:10", "IATANum": "1003081P", "voucherNum": 3255489, "gateway": "EPS", "paidCurr": "BHD", "paidAmt": 157.46, "baseCurr": "BHD", "baseAmt": 157.46, "userID": "WEB_TA", "channelID": 16, "voucherNumFull": "QHBY94", "authCode": "QHBY94", "reference": "A5314028", "externalReference": "A5314028", "tranId": "21232189", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1", "resExternalPaymentID": 21232189}, {"paymentID": *********, "paxID": 267971955, "method": "VCHR", "status": "1", "paidDate": "2025-05-13T14:31:08", "voucherNum": 3259735, "paidCurr": "BHD", "paidAmt": -168.5, "baseCurr": "BHD", "baseAmt": -168.5, "userID": "nusrath.shaik", "channelID": 1, "paymentComment": "TEST <PERSON>NR CREATED FOR PRODUCTION VALIDATION.", "voucherNumFull": "LT9K5C", "reference": "Cancel Refund Voucher Flier", "deviceTerminalID": null, "RRNNumber": null, "cardholderSignatureRequired": false, "failureReason": null, "paymentReceiptURL": null, "autoRefundStatus": null, "merchantID": "0", "exchangeRate": "1"}], "OAFlights": null, "physicalFlights": [{"key": "16804773:181015:2025-07-15T03:35:00 PM", "LFID": 16804773, "PFID": 181015, "org": "BAH", "dest": "DXB", "depDate": "2025-07-15T15:35:00", "depTime": "2025-07-15T15:35:00", "arrTime": "2025-07-15T17:55:00", "carrier": "FZ", "flightNum": "022", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73X", "mrktCarrier": "FZ", "mrktFlightNum": "022", "flightStatus": "OPEN", "originMetroGroup": "BAH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4800, "reaccomChangeAlert": false, "originName": "Bahrain", "destinationName": "Dubai International Airport", "isActive": true, "changeType": "AC", "flightChangeTime": "3/26/2025 10:36:40 AM"}, {"key": "16804773:185998:2025-07-15T07:50:00 PM", "LFID": 16804773, "PFID": 185998, "org": "DXB", "dest": "JED", "depDate": "2025-07-15T19:50:00", "depTime": "2025-07-15T19:50:00", "arrTime": "2025-07-15T21:55:00", "carrier": "FZ", "flightNum": "807", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73B", "mrktCarrier": "FZ", "mrktFlightNum": "807", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "JED", "operatingCarrier": "FZ", "flightDuration": 11100, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Jeddah", "isActive": true, "changeType": "AC", "flightChangeTime": "3/26/2025 10:36:40 AM"}, {"key": "16534002:184291:2026-01-07T01:40:00 AM", "LFID": 16534002, "PFID": 184291, "org": "BAH", "dest": "DXB", "depDate": "2026-01-07T01:40:00", "depTime": "2026-01-07T01:40:00", "arrTime": "2026-01-07T03:55:00", "carrier": "FZ", "flightNum": "030", "depTerminal": "Terminal 1", "arrTerminal": "Terminal 2", "flightOrder": 1, "aircraftType": "73D", "mrktCarrier": "FZ", "mrktFlightNum": "030", "flightStatus": "OPEN", "originMetroGroup": "BAH", "destinationMetroGroup": "DXB", "operatingCarrier": "FZ", "flightDuration": 4500, "reaccomChangeAlert": false, "originName": "Bahrain", "destinationName": "Dubai International Airport", "isActive": true}, {"key": "16534002:184595:2026-01-07T09:25:00 AM", "LFID": 16534002, "PFID": 184595, "org": "DXB", "dest": "JED", "depDate": "2026-01-07T09:25:00", "depTime": "2026-01-07T09:25:00", "arrTime": "2026-01-07T11:45:00", "carrier": "FZ", "flightNum": "909", "depTerminal": "Terminal 2", "arrTerminal": "Terminal 1", "flightOrder": 2, "aircraftType": "73R", "mrktCarrier": "FZ", "mrktFlightNum": "909", "flightStatus": "OPEN", "originMetroGroup": "DXB", "destinationMetroGroup": "JED", "operatingCarrier": "FZ", "flightDuration": 12000, "reaccomChangeAlert": false, "originName": "Dubai International Airport", "destinationName": "Jeddah", "isActive": true}], "chargeInfos": [{"recNum": 1, "charges": [{"chargeID": **********, "codeType": "INSU", "amt": 4.98, "curr": "BHD", "originalAmt": 4.98, "originalCurr": "BHD", "status": 0, "exchRate": 1, "billDate": "2025-05-05T15:14:59", "desc": "INSU", "comment": "Insurance Charge", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 4.98, "approveCode": 0}], "isSSR": true, "parameter2Name": "PROVIDER", "parameter2Value": "AIG", "ChargeBookDate": "2025-05-05T15:14:58"}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": -3.8, "curr": "BHD", "originalAmt": -3.8, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:20", "desc": "YQ - DUMMY", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -3.8, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": -0.6, "curr": "BHD", "originalAmt": -0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:20", "desc": "Advanced passenger information fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333059799, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": -0.6, "approveCode": 0}]}, {"chargeID": 1333488948, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": -4.6, "curr": "BHD", "originalAmt": -4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:20", "desc": "Passenger Facilities Charge.", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333059798, "paymentMap": [{"key": "1333488948:*********", "paymentID": *********, "amt": -4.6, "approveCode": 0}]}, {"chargeID": 1333489010, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": **********, "amt": -0.3, "curr": "BHD", "originalAmt": -0.3, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:20", "desc": "Passenger Service Fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333059796, "paymentMap": [{"key": "1333489010:*********", "paymentID": *********, "amt": -0.3, "approveCode": 0}]}, {"chargeID": 1333489011, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": **********, "amt": -10, "curr": "BHD", "originalAmt": -10, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:20", "desc": "Passenger Service Fee", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333059797, "paymentMap": [{"key": "1333489011:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1333059800, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": **********, "amt": 0.8, "curr": "BHD", "originalAmt": 0.8, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:14:59", "desc": "Security Charges", "comment": "Security Charges", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333059800:*********", "paymentID": *********, "amt": 0.8, "approveCode": 0}]}, {"chargeID": 1333059798, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": **********, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:14:59", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333059798:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1333059799, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": **********, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:14:59", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333059799:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1333059796, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": **********, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:14:59", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333059796:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0}]}, {"chargeID": 1333059797, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": **********, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:14:59", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333059797:*********", "paymentID": *********, "amt": 10, "approveCode": 0}]}, {"chargeID": **********, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": **********, "amt": 3.8, "curr": "BHD", "originalAmt": 3.8, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T15:14:59", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 3.8, "approveCode": 0}]}, {"chargeID": 1333488947, "codeType": "AIR", "amt": -149.2, "curr": "BHD", "originalAmt": -149.2, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-05T23:48:20", "desc": "WEB:AIR", "comment": "Cancel Due To Modify", "reasonID": 1, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": **********, "paymentMap": [{"key": "1333488947:*********", "paymentID": *********, "amt": -149.2, "approveCode": 0}]}, {"chargeID": **********, "codeType": "AIR", "amt": 149.2, "curr": "BHD", "originalAmt": 149.2, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-05T15:14:59", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "**********:*********", "paymentID": *********, "amt": 149.2, "approveCode": 0}]}, {"chargeID": 1333489013, "codeType": "PNLT", "amt": 153, "curr": "BHD", "originalAmt": 153, "originalCurr": "BHD", "status": 1, "billDate": "2025-05-05T23:48:20", "desc": "Penalty AddedDueToModify FZ  030 BAH  - DXB  07-Jan-2026", "comment": "Penalty Added Due To Modify", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333489013:*********", "paymentID": *********, "amt": 153, "approveCode": 0}]}]}, {"recNum": 2, "charges": [{"chargeID": 1333489014, "codeType": "INSU", "amt": 3.66, "curr": "BHD", "originalAmt": 3.66, "originalCurr": "BHD", "status": 0, "exchRate": 1, "billDate": "2025-05-05T23:48:21", "desc": "INSU", "reasonID": 0, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333489014:*********", "paymentID": *********, "amt": 3.66, "approveCode": 0}], "isSSR": true, "parameter1Name": "INSURANCE_DATA", "parameter1Value": "{\"Currency\":\"USD\",\"Fx\":\"2.65\",\"Premium\":\"9.72\",\"Tax\":\"0.45\",\"SegPaxCount\":\"1\"}", "ChargeBookDate": "2025-05-05T23:48:21"}, {"chargeID": 1333489045, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1333489044, "amt": 0.3, "curr": "BHD", "originalAmt": 0.3, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:20", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333489045:*********", "paymentID": *********, "amt": 0.3, "approveCode": 0}]}, {"chargeID": 1333489048, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1333489044, "amt": 0.6, "curr": "BHD", "originalAmt": 0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:20", "desc": "Advanced passenger information fee", "comment": "Advanced passenger information fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333489048:*********", "paymentID": *********, "amt": 0.6, "approveCode": 0}]}, {"chargeID": 1333489050, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1333489044, "amt": 3.8, "curr": "BHD", "originalAmt": 3.8, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:21", "desc": "YQ - DUMMY", "comment": "YQ - DUMMY", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333489050:*********", "paymentID": *********, "amt": 3.8, "approveCode": 0}]}, {"chargeID": 1333489049, "codeType": "TAX", "taxID": 8025, "taxCode": "E3", "taxChargeID": 1333489044, "amt": 0.8, "curr": "BHD", "originalAmt": 0.8, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:21", "desc": "Security Charges", "comment": "Security Charges", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333489049:*********", "paymentID": *********, "amt": 0.8, "approveCode": 0}]}, {"chargeID": 1333489047, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1333489044, "amt": 4.6, "curr": "BHD", "originalAmt": 4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:21", "desc": "Passenger Facilities Charge.", "comment": "Passenger Facilities Charge.", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333489047:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}]}, {"chargeID": 1333489046, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1333489044, "amt": 10, "curr": "BHD", "originalAmt": 10, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-05T23:48:21", "desc": "Passenger Service Fee", "comment": "Passenger Service Fee", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333489046:*********", "paymentID": *********, "amt": 4.6, "approveCode": 0}, {"key": "1333489046:*********", "paymentID": *********, "amt": 5.4, "approveCode": 0}]}, {"chargeID": 1344554013, "codeType": "TAX", "taxID": 9186, "taxCode": "BH", "taxChargeID": 1344553988, "amt": -10, "curr": "BHD", "originalAmt": -10, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:31:08", "desc": "Passenger Service Fee", "comment": "TEST <PERSON>NR CREATED FOR PRODUCTION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333489046, "paymentMap": [{"key": "1344554013:*********", "paymentID": *********, "amt": -10, "approveCode": 0}]}, {"chargeID": 1344554015, "codeType": "TAX", "taxID": 11947, "taxCode": "F6", "taxChargeID": 1344553988, "amt": -4.6, "curr": "BHD", "originalAmt": -4.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:31:08", "desc": "Passenger Facilities Charge.", "comment": "TEST <PERSON>NR CREATED FOR PRODUCTION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333489047, "paymentMap": [{"key": "1344554015:*********", "paymentID": *********, "amt": -4.6, "approveCode": 0}]}, {"chargeID": 1344554017, "codeType": "TAX", "taxID": 4246, "taxCode": "YQ", "taxChargeID": 1344553988, "amt": -3.8, "curr": "BHD", "originalAmt": -3.8, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:31:08", "desc": "YQ - DUMMY", "comment": "TEST <PERSON>NR CREATED FOR PRODUCTION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333489050, "paymentMap": [{"key": "1344554017:*********", "paymentID": *********, "amt": -3.8, "approveCode": 0}]}, {"chargeID": 1344554018, "codeType": "TAX", "taxID": 10386, "taxCode": "ZR", "taxChargeID": 1344553988, "amt": -0.6, "curr": "BHD", "originalAmt": -0.6, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:31:08", "desc": "Advanced passenger information fee", "comment": "TEST <PERSON>NR CREATED FOR PRODUCTION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333489048, "paymentMap": [{"key": "1344554018:*********", "paymentID": *********, "amt": -0.6, "approveCode": 0}]}, {"chargeID": 1344554020, "codeType": "TAX", "taxID": 12670, "taxCode": "HM", "taxChargeID": 1344553988, "amt": -0.3, "curr": "BHD", "originalAmt": -0.3, "originalCurr": "BHD", "status": 0, "exchRate": 0, "billDate": "2025-05-13T14:31:08", "desc": "Passenger Service Fee", "comment": "TEST <PERSON>NR CREATED FOR PRODUCTION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333489045, "paymentMap": [{"key": "1344554020:*********", "paymentID": *********, "amt": -0.3, "approveCode": 0}]}, {"chargeID": 1333489044, "codeType": "AIR", "amt": 149.2, "curr": "BHD", "originalAmt": 149.2, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-05T23:48:20", "desc": "WEB:AIR", "comment": "WEB:AIR", "reasonID": 2, "channelID": 2, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "paymentMap": [{"key": "1333489044:*********", "paymentID": *********, "amt": 149.2, "approveCode": 0}]}, {"chargeID": 1344553988, "codeType": "AIR", "amt": -149.2, "curr": "BHD", "originalAmt": -149.2, "originalCurr": "BHD", "status": 0, "billDate": "2025-05-13T14:31:08", "desc": "WEB:AIR", "comment": "TEST <PERSON>NR CREATED FOR PRODUCTION VALIDATION.", "reasonID": 7, "channelID": 1, "basePoints": 0, "tierPoints": 0, "bonusPoints": 0, "bonusTierPoints": 0, "originalChargeID": 1333489044, "paymentMap": [{"key": "1344553988:*********", "paymentID": *********, "amt": -149.2, "approveCode": 0}]}]}], "parentPNRs": [], "childPNRs": []}